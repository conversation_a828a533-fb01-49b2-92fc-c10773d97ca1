1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.oney.WebRTCModule" >
5
6    <uses-sdk android:minSdkVersion="24" />
7
8    <application>
8-->K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:5:5-10:19
9        <service
9-->K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:6:9-9:19
10            android:name="com.oney.WebRTCModule.MediaProjectionService"
10-->K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:7:17-55
11            android:foregroundServiceType="mediaProjection" >
11-->K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:8:17-64
12        </service>
13    </application>
14
15</manifest>
