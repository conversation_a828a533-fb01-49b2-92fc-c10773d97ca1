{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11795,11866,11927,11994,12060,12138,12217,12309,12395", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "11861,11922,11989,12055,12133,12212,12304,12390,12460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "68", "endOffsets": "270"}, "to": {"startLines": "269", "startColumns": "4", "startOffsets": "23043", "endColumns": "72", "endOffsets": "23111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "86,161,162,163", "startColumns": "4,4,4,4", "startOffsets": "7720,13765,13866,13977", "endColumns": "114,100,110,100", "endOffsets": "7830,13861,13972,14073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,261,386,525,668,802,937,1081,1177,1320,1468", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "163,256,381,520,663,797,932,1076,1172,1315,1463,1584"}, "to": {"startLines": "85,87,164,165,166,167,168,169,170,171,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7607,7835,14078,14203,14342,14485,14619,14754,14898,14994,15137,15285", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "7715,7923,14198,14337,14480,14614,14749,14893,14989,15132,15280,15401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3431,3496", "endColumns": "64,65", "endOffsets": "3491,3557"}, "to": {"startLines": "152,153", "startColumns": "4,4", "startOffsets": "13135,13200", "endColumns": "64,65", "endOffsets": "13195,13261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1843,1992,2135,2202,2284,2356,2429,2528,2627,2691,2761,2814,2872,2920,2981,3046,3112,3174,3242,3306,3365,3431,3483,3548,3626,3704,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,51,64,77,77,56,68", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1838,1987,2130,2197,2279,2351,2424,2523,2622,2686,2756,2809,2867,2915,2976,3041,3107,3169,3237,3301,3360,3426,3478,3543,3621,3699,3756,3825"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,580,9705,9786,9867,9953,10057,10149,10222,10285,10375,10465,10530,10593,10660,10728,10877,11026,11169,11236,11318,11390,11463,11562,11661,11725,12465,12518,12576,12624,12685,12750,12816,12878,12946,13010,13069,13266,13318,13383,13461,13539,13596", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,51,64,77,77,56,68", "endOffsets": "376,575,761,9781,9862,9948,10052,10144,10217,10280,10370,10460,10525,10588,10655,10723,10872,11021,11164,11231,11313,11385,11458,11557,11656,11720,11790,12513,12571,12619,12680,12745,12811,12873,12941,13005,13064,13130,13313,13378,13456,13534,13591,13660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,210,281,350,432,501,568,650,734,823,906,976,1062,1151,1226,1307,1388,1465,1540,1613,1700,1777,1858,1932", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "124,205,276,345,427,496,563,645,729,818,901,971,1057,1146,1221,1302,1383,1460,1535,1608,1695,1772,1853,1927,2010"}, "to": {"startLines": "50,66,174,176,177,179,199,200,201,248,249,250,251,256,257,258,259,260,261,262,263,265,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3629,5185,15406,15547,15616,15757,17299,17366,17448,21342,21431,21514,21584,21989,22078,22153,22234,22315,22392,22467,22540,22728,22805,22886,22960", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "3698,5261,15472,15611,15693,15821,17361,17443,17527,21426,21509,21579,21665,22073,22148,22229,22310,22387,22462,22535,22622,22800,22881,22955,23038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6327", "endColumns": "128", "endOffsets": "6451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5266,5373,5546,5683,5790,5951,6085,6211,6456,6626,6734,6909,7047,7209,7393,7458,7525", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "5368,5541,5678,5785,5946,6080,6206,6322,6621,6729,6904,7042,7204,7388,7453,7520,7602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1142,1242,1312,1371,1469,1531,1595,1654,1726,1789,1843,1960,2017,2079,2133,2205,2340,2423,2502,2598,2681,2759,2900,2984,3066,3214,3304,3382,3435,3494,3560,3631,3710,3781,3864,3940,4018,4090,4163,4267,4356,4428,4522,4621,4695,4767,4868,4918,5003,5069,5159,5248,5310,5374,5437,5504,5620,5733,5842,5947,6004,6067,6150,6235,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1137,1237,1307,1366,1464,1526,1590,1649,1721,1784,1838,1955,2012,2074,2128,2200,2335,2418,2497,2593,2676,2754,2895,2979,3061,3209,3299,3377,3430,3489,3555,3626,3705,3776,3859,3935,4013,4085,4158,4262,4351,4423,4517,4616,4690,4762,4863,4913,4998,5064,5154,5243,5305,5369,5432,5499,5615,5728,5837,5942,5999,6062,6145,6230,6304,6382"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,89,90,160,175,178,180,181,182,183,184,185,186,187,188,189,190,191,192,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,253,254,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3703,3782,3860,3946,4046,4875,4976,5102,8002,8067,13665,15477,15698,15826,15924,15986,16050,16109,16181,16244,16298,16415,16472,16534,16588,16660,17532,17615,17694,17790,17873,17951,18092,18176,18258,18406,18496,18574,18627,18686,18752,18823,18902,18973,19056,19132,19210,19282,19355,19459,19548,19620,19714,19813,19887,19959,20060,20110,20195,20261,20351,20440,20502,20566,20629,20696,20812,20925,21034,21139,21196,21259,21752,21837,21911", "endLines": "22,51,52,53,54,55,63,64,65,89,90,160,175,178,180,181,182,183,184,185,186,187,188,189,190,191,192,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,253,254,255", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "928,3777,3855,3941,4041,4133,4971,5097,5180,8062,8127,13760,15542,15752,15919,15981,16045,16104,16176,16239,16293,16410,16467,16529,16583,16655,16790,17610,17689,17785,17868,17946,18087,18171,18253,18401,18491,18569,18622,18681,18747,18818,18897,18968,19051,19127,19205,19277,19350,19454,19543,19615,19709,19808,19882,19954,20055,20105,20190,20256,20346,20435,20497,20561,20624,20691,20807,20920,21029,21134,21191,21254,21337,21832,21906,21984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "933,1047,1147,1259,1345,1451,1574,1656,1734,1825,1918,2013,2107,2208,2301,2396,2493,2584,2677,2758,2864,2968,3066,3172,3276,3378,3532,21670", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "1042,1142,1254,1340,1446,1569,1651,1729,1820,1913,2008,2102,2203,2296,2391,2488,2579,2672,2753,2859,2963,3061,3167,3271,3373,3527,3624,21747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "56,57,58,59,60,61,62,264", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4138,4237,4339,4437,4534,4642,4753,22627", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "4232,4334,4432,4529,4637,4748,4870,22723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,239,321,414,511,607,694,778,875,968,1036,1141,1233,1335,1416,1516,1594,1702,1780,1846,1924,2007,2102", "endColumns": "73,109,81,92,96,95,86,83,96,92,67,104,91,101,80,99,77,107,77,65,77,82,94,103", "endOffsets": "124,234,316,409,506,602,689,773,870,963,1031,1136,1228,1330,1411,1511,1589,1697,1775,1841,1919,2002,2097,2201"}, "to": {"startLines": "88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,193,194,195,196,197,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7928,8132,8242,8324,8417,8514,8610,8697,8781,8878,8971,9039,9144,9236,9338,9419,9519,9597,16795,16873,16939,17017,17100,17195", "endColumns": "73,109,81,92,96,95,86,83,96,92,67,104,91,101,80,99,77,107,77,65,77,82,94,103", "endOffsets": "7997,8237,8319,8412,8509,8605,8692,8776,8873,8966,9034,9139,9231,9333,9414,9514,9592,9700,16868,16934,17012,17095,17190,17294"}}]}]}