{"version": 3, "file": "AppleAuthentication.types.js", "sourceRoot": "", "sources": ["../src/AppleAuthentication.types.ts"], "names": [], "mappings": "AA0MA,2BAA2B;AAC3B;;;;;;;;;GASG;AACH,MAAM,CAAN,IAAY,wBAGX;AAHD,WAAY,wBAAwB;IAClC,iFAAa,CAAA;IACb,yEAAS,CAAA;AACX,CAAC,EAHW,wBAAwB,KAAxB,wBAAwB,QAGnC;AAED,2BAA2B;AAC3B,MAAM,CAAN,IAAY,4BAQX;AARD,WAAY,4BAA4B;IACtC;;OAEG;IACH,uFAAY,CAAA;IACZ,iFAAS,CAAA;IACT,qFAAW,CAAA;IACX,mFAAU,CAAA;AACZ,CAAC,EARW,4BAA4B,KAA5B,4BAA4B,QAQvC;AAED,2BAA2B;AAC3B;;;;;;GAMG;AACH,MAAM,CAAN,IAAY,kCAKX;AALD,WAAY,kCAAkC;IAC5C,iGAAW,CAAA;IACX,uGAAc,CAAA;IACd,qGAAa,CAAA;IACb,yGAAe,CAAA;AACjB,CAAC,EALW,kCAAkC,KAAlC,kCAAkC,QAK7C;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAN,IAAY,sCAaX;AAbD,WAAY,sCAAsC;IAChD;;OAEG;IACH,iHAAe,CAAA;IACf;;OAEG;IACH,yGAAW,CAAA;IACX;;OAEG;IACH,iHAAe,CAAA;AACjB,CAAC,EAbW,sCAAsC,KAAtC,sCAAsC,QAajD;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,6BAcX;AAdD,WAAY,6BAA6B;IACvC;;OAEG;IACH,uFAAW,CAAA;IACX;;OAEG;IACH,yFAAY,CAAA;IACZ;;;OAGG;IACH,uFAAW,CAAA;AACb,CAAC,EAdW,6BAA6B,KAA7B,6BAA6B,QAcxC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,8BAaX;AAbD,WAAY,8BAA8B;IACxC;;OAEG;IACH,qFAAS,CAAA;IACT;;OAEG;IACH,qGAAiB,CAAA;IACjB;;OAEG;IACH,qFAAS,CAAA;AACX,CAAC,EAbW,8BAA8B,KAA9B,8BAA8B,QAazC", "sourcesContent": ["import type { StyleProp, ViewStyle, ViewProps } from 'react-native';\n\n// @needsAudit\nexport declare type AppleAuthenticationButtonProps = ViewProps & {\n  /**\n   * The method to call when the user presses the button. You should call [`AppleAuthentication.signInAsync`](#appleauthenticationisavailableasync)\n   * in here.\n   */\n  onPress: () => void;\n  /**\n   * The type of button text to display (\"Sign In with Apple\" vs. \"Continue with Apple\").\n   */\n  buttonType: AppleAuthenticationButtonType;\n  /**\n   * The Apple-defined color scheme to use to display the button.\n   */\n  buttonStyle: AppleAuthenticationButtonStyle;\n  /**\n   * The border radius to use when rendering the button. This works similarly to\n   * `style.borderRadius` in other Views.\n   */\n  cornerRadius?: number;\n  /**\n   * The custom style to apply to the button. Should not include `backgroundColor` or `borderRadius`\n   * properties.\n   */\n  style?: StyleProp<Omit<ViewStyle, 'backgroundColor' | 'borderRadius'>>;\n};\n\n// @needsAudit\n/**\n * The options you can supply when making a call to [`AppleAuthentication.signInAsync()`](#appleauthenticationsigninasyncoptions).\n * None of these options are required.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationopenidrequest)\n * for more details.\n */\nexport type AppleAuthenticationSignInOptions = {\n  /**\n   * Array of user information scopes to which your app is requesting access. Note that the user can\n   * choose to deny your app access to any scope at the time of logging in. You will still need to\n   * handle `null` values for any scopes you request. Additionally, note that the requested scopes\n   * will only be provided to you the first time each user signs into your app; in subsequent\n   * requests they will be `null`. Defaults to `[]` (no scopes).\n   */\n  requestedScopes?: AppleAuthenticationScope[];\n\n  /**\n   * An arbitrary string that is returned unmodified in the corresponding credential after a\n   * successful authentication. This can be used to verify that the response was from the request\n   * you made and avoid replay attacks. More information on this property is available in the\n   * OAuth 2.0 protocol [RFC6749](https://tools.ietf.org/html/rfc6749#section-10.12).\n   */\n  state?: string;\n\n  /**\n   * An arbitrary string that is used to prevent replay attacks. See more information on this in the\n   * [OpenID Connect specification](https://openid.net/specs/openid-connect-core-1_0.html#CodeFlowSteps).\n   */\n  nonce?: string;\n};\n\n// @needsAudit @docsMissing\n/**\n * The options you can supply when making a call to [`AppleAuthentication.refreshAsync()`](#appleauthenticationrefreshasyncoptions).\n * You must include the ID string of the user whose credentials you'd like to refresh.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationopenidrequest)\n * for more details.\n */\nexport type AppleAuthenticationRefreshOptions = {\n  user: string;\n\n  /**\n   * Array of user information scopes to which your app is requesting access. Note that the user can\n   * choose to deny your app access to any scope at the time of logging in. You will still need to\n   * handle `null` values for any scopes you request. Additionally, note that the requested scopes\n   * will only be provided to you the first time each user signs into your app; in subsequent\n   * requests they will be `null`. Defaults to `[]` (no scopes).\n   */\n  requestedScopes?: AppleAuthenticationScope[];\n\n  /**\n   * An arbitrary string that is returned unmodified in the corresponding credential after a\n   * successful authentication. This can be used to verify that the response was from the request\n   * you made and avoid replay attacks. More information on this property is available in the\n   * OAuth 2.0 protocol [RFC6749](https://tools.ietf.org/html/rfc6749#section-10.12).\n   */\n  state?: string;\n};\n\n// @needsAudit\n/**\n * The options you can supply when making a call to [`AppleAuthentication.signOutAsync()`](#appleauthenticationsignoutasyncoptions).\n * You must include the ID string of the user to sign out.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationopenidrequest)\n * for more details.\n */\nexport type AppleAuthenticationSignOutOptions = {\n  user: string;\n\n  /**\n   * An arbitrary string that is returned unmodified in the corresponding credential after a\n   * successful authentication. This can be used to verify that the response was from the request\n   * you made and avoid replay attacks. More information on this property is available in the\n   * OAuth 2.0 protocol [RFC6749](https://tools.ietf.org/html/rfc6749#section-10.12).\n   */\n  state?: string;\n};\n\n// @needsAudit\n/**\n * The object type returned from a successful call to [`AppleAuthentication.signInAsync()`](#appleauthenticationsigninasyncoptions),\n * [`AppleAuthentication.refreshAsync()`](#appleauthenticationrefreshasyncoptions), or [`AppleAuthentication.signOutAsync()`](#appleauthenticationsignoutasyncoptions)\n * which contains all of the pertinent user and credential information.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationappleidcredential)\n * for more details.\n */\nexport type AppleAuthenticationCredential = {\n  /**\n   * An identifier associated with the authenticated user. You can use this to check if the user is\n   * still authenticated later. This is stable and can be shared across apps released under the same\n   * development team. The same user will have a different identifier for apps released by other\n   * developers.\n   */\n  user: string;\n\n  /**\n   * An arbitrary string that your app provided as `state` in the request that generated the\n   * credential. Used to verify that the response was from the request you made. Can be used to\n   * avoid replay attacks. If you did not provide `state` when making the sign-in request, this field\n   * will be `null`.\n   */\n  state: string | null;\n\n  /**\n   * The user's name. May be `null` or contain `null` values if you didn't request the `FULL_NAME`\n   * scope, if the user denied access, or if this is not the first time the user has signed into\n   * your app.\n   */\n  fullName: AppleAuthenticationFullName | null;\n\n  /**\n   * The user's email address. Might not be present if you didn't request the `EMAIL` scope. May\n   * also be null if this is not the first time the user has signed into your app. If the user chose\n   * to withhold their email address, this field will instead contain an obscured email address with\n   * an Apple domain.\n   */\n  email: string | null;\n\n  /**\n   * A value that indicates whether the user appears to the system to be a real person.\n   */\n  realUserStatus: AppleAuthenticationUserDetectionStatus;\n\n  /**\n   * A JSON Web Token (JWT) that securely communicates information about the user to your app.\n   */\n  identityToken: string | null;\n\n  /**\n   * A short-lived session token used by your app for proof of authorization when interacting with\n   * the app's server counterpart. Unlike `user`, this is ephemeral and will change each session.\n   */\n  authorizationCode: string | null;\n};\n\n// @needsAudit @docsMissing\n/**\n * An object representing the tokenized portions of the user's full name. Any of all of the fields\n * may be `null`. Only applicable fields that the user has allowed your app to access will be nonnull.\n */\nexport type AppleAuthenticationFullName = {\n  namePrefix: string | null;\n  givenName: string | null;\n  middleName: string | null;\n  familyName: string | null;\n  nameSuffix: string | null;\n  nickname: string | null;\n};\n\n// @needsAudit @docsMissing\n/**\n * A value to specify the style for formatting a name.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/foundation/personnamecomponentsformatter)\n * for more details.\n */\nexport type AppleAuthenticationFullNameFormatStyle =\n  | 'default'\n  | 'short'\n  | 'medium'\n  | 'long'\n  | 'abbreviated';\n\n// @needsAudit @docsMissing\n/**\n * An enum whose values specify scopes you can request when calling [`AppleAuthentication.signInAsync()`](#appleauthenticationsigninasyncoptions).\n *\n * > Note that it is possible that you will not be granted all of the scopes which you request.\n * > You will still need to handle null values for any fields you request.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationscope)\n * for more details.\n */\nexport enum AppleAuthenticationScope {\n  FULL_NAME = 0,\n  EMAIL = 1,\n}\n\n// @needsAudit @docsMissing\nexport enum AppleAuthenticationOperation {\n  /**\n   * An operation that depends on the particular kind of credential provider.\n   */\n  IMPLICIT = 0,\n  LOGIN = 1,\n  REFRESH = 2,\n  LOGOUT = 3,\n}\n\n// @needsAudit @docsMissing\n/**\n * An enum whose values specify state of the credential when checked with [`AppleAuthentication.getCredentialStateAsync()`](#appleauthenticationgetcredentialstateasyncuser).\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationappleidprovidercredentialstate)\n * for more details.\n */\nexport enum AppleAuthenticationCredentialState {\n  REVOKED = 0,\n  AUTHORIZED = 1,\n  NOT_FOUND = 2,\n  TRANSFERRED = 3,\n}\n\n// @needsAudit\n/**\n * An enum whose values specify the system's best guess for how likely the current user is a real person.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asuserdetectionstatus)\n * for more details.\n */\nexport enum AppleAuthenticationUserDetectionStatus {\n  /**\n   * The system does not support this determination and there is no data.\n   */\n  UNSUPPORTED = 0,\n  /**\n   * The system has not determined whether the user might be a real person.\n   */\n  UNKNOWN = 1,\n  /**\n   * The user appears to be a real person.\n   */\n  LIKELY_REAL = 2,\n}\n\n// @needsAudit\n/**\n * An enum whose values control which pre-defined text to use when rendering an [`AppleAuthenticationButton`](#appleauthenticationbutton).\n */\nexport enum AppleAuthenticationButtonType {\n  /**\n   * \"Sign in with Apple\"\n   */\n  SIGN_IN = 0,\n  /**\n   * \"Continue with Apple\"\n   */\n  CONTINUE = 1,\n  /**\n   * \"Sign up with Apple\"\n   * @platform ios 13.2+\n   */\n  SIGN_UP = 2,\n}\n\n// @needsAudit\n/**\n * An enum whose values control which pre-defined color scheme to use when rendering an [`AppleAuthenticationButton`](#appleauthenticationbutton).\n */\nexport enum AppleAuthenticationButtonStyle {\n  /**\n   * White button with black text.\n   */\n  WHITE = 0,\n  /**\n   * White button with a black outline and black text.\n   */\n  WHITE_OUTLINE = 1,\n  /**\n   * Black button with white text.\n   */\n  BLACK = 2,\n}\n"]}