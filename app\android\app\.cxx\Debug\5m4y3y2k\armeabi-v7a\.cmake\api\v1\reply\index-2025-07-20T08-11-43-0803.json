{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Android/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Android/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Android/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "C:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-9b7fcd4ccf497d6cfdda.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-3c57a5758d1942750452.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-657ba5df7171d1e82f76.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-3c57a5758d1942750452.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-657ba5df7171d1e82f76.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-9b7fcd4ccf497d6cfdda.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}