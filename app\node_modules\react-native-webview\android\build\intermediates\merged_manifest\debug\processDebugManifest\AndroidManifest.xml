<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.reactnativecommunity.webview" >

    <uses-sdk android:minSdkVersion="24" />

    <queries>
        <intent>
            <action android:name="org.chromium.intent.action.PAY" />
        </intent>
        <intent>
            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
        </intent>
        <intent>
            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
        </intent>
    </queries>

    <application>
        <provider
            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
    </application>

</manifest>