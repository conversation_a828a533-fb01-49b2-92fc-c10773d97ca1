// Generated by view binder compiler. Do not edit!
package expo.modules.devlauncher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import expo.modules.devlauncher.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ErrorFragmentBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final TextView errorDetails;

  @NonNull
  public final LinearLayout errorFooter;

  @NonNull
  public final LinearLayout errorFooterContent;

  @NonNull
  public final LinearLayout errorMainContent;

  @NonNull
  public final ListView errorStack;

  @NonNull
  public final TextView errorTitle;

  @NonNull
  public final Button homeButton;

  @NonNull
  public final Button reloadButton;

  private ErrorFragmentBinding(@NonNull RelativeLayout rootView, @NonNull TextView errorDetails,
      @NonNull LinearLayout errorFooter, @NonNull LinearLayout errorFooterContent,
      @NonNull LinearLayout errorMainContent, @NonNull ListView errorStack,
      @NonNull TextView errorTitle, @NonNull Button homeButton, @NonNull Button reloadButton) {
    this.rootView = rootView;
    this.errorDetails = errorDetails;
    this.errorFooter = errorFooter;
    this.errorFooterContent = errorFooterContent;
    this.errorMainContent = errorMainContent;
    this.errorStack = errorStack;
    this.errorTitle = errorTitle;
    this.homeButton = homeButton;
    this.reloadButton = reloadButton;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ErrorFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ErrorFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.error_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ErrorFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.errorDetails;
      TextView errorDetails = ViewBindings.findChildViewById(rootView, id);
      if (errorDetails == null) {
        break missingId;
      }

      id = R.id.error_footer;
      LinearLayout errorFooter = ViewBindings.findChildViewById(rootView, id);
      if (errorFooter == null) {
        break missingId;
      }

      id = R.id.error_footer_content;
      LinearLayout errorFooterContent = ViewBindings.findChildViewById(rootView, id);
      if (errorFooterContent == null) {
        break missingId;
      }

      id = R.id.error_main_content;
      LinearLayout errorMainContent = ViewBindings.findChildViewById(rootView, id);
      if (errorMainContent == null) {
        break missingId;
      }

      id = R.id.error_stack;
      ListView errorStack = ViewBindings.findChildViewById(rootView, id);
      if (errorStack == null) {
        break missingId;
      }

      id = R.id.error_title;
      TextView errorTitle = ViewBindings.findChildViewById(rootView, id);
      if (errorTitle == null) {
        break missingId;
      }

      id = R.id.homeButton;
      Button homeButton = ViewBindings.findChildViewById(rootView, id);
      if (homeButton == null) {
        break missingId;
      }

      id = R.id.reloadButton;
      Button reloadButton = ViewBindings.findChildViewById(rootView, id);
      if (reloadButton == null) {
        break missingId;
      }

      return new ErrorFragmentBinding((RelativeLayout) rootView, errorDetails, errorFooter,
          errorFooterContent, errorMainContent, errorStack, errorTitle, homeButton, reloadButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
