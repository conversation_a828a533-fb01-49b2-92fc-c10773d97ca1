{"version": 3, "file": "ExpoAppleAuthenticationButton.js", "sourceRoot": "", "sources": ["../src/ExpoAppleAuthenticationButton.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,IAAI,6BAAkC,CAAC;AAEvC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;IAC1B,6BAA6B,GAAG,wBAAwB,CAAC,yBAAyB,CAAC,CAAC;AACtF,CAAC;AAED,eAAe,6BAA6B,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nlet ExpoAppleAuthenticationButton: any;\n\nif (Platform.OS === 'ios') {\n  ExpoAppleAuthenticationButton = requireNativeViewManager('ExpoAppleAuthentication');\n}\n\nexport default ExpoAppleAuthenticationButton;\n"]}