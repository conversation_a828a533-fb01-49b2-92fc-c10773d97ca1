{"name": "expo-apple-authentication", "version": "7.2.4", "description": "A package that provides 'Sign in with Apple' capability for Expo and React Native apps.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-apple-authentiocation", "apple-authentication", "sign-in-with-apple"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-apple-authentication"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/apple-authentication/", "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}