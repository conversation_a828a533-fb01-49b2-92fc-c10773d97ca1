  Uri android.net  Any expo.modules.updatesinterface  Boolean expo.modules.updatesinterface  	Exception expo.modules.updatesinterface  HashMap expo.modules.updatesinterface  Int expo.modules.updatesinterface  
JSONObject expo.modules.updatesinterface  String expo.modules.updatesinterface  Update expo.modules.updatesinterface  UpdateCallback expo.modules.updatesinterface  UpdatesControllerRegistry expo.modules.updatesinterface  UpdatesInterface expo.modules.updatesinterface  UpdatesInterfaceCallbacks expo.modules.updatesinterface  Uri expo.modules.updatesinterface  
WeakReference expo.modules.updatesinterface  Any .expo.modules.updatesinterface.UpdatesInterface  Boolean .expo.modules.updatesinterface.UpdatesInterface  	Exception .expo.modules.updatesinterface.UpdatesInterface  HashMap .expo.modules.updatesinterface.UpdatesInterface  Int .expo.modules.updatesinterface.UpdatesInterface  
JSONObject .expo.modules.updatesinterface.UpdatesInterface  String .expo.modules.updatesinterface.UpdatesInterface  Update .expo.modules.updatesinterface.UpdatesInterface  UpdateCallback .expo.modules.updatesinterface.UpdatesInterface  UpdatesInterfaceCallbacks .expo.modules.updatesinterface.UpdatesInterface  Uri .expo.modules.updatesinterface.UpdatesInterface  
WeakReference .expo.modules.updatesinterface.UpdatesInterface  	Exception 	java.lang  
WeakReference 
java.lang.ref  HashMap 	java.util  
JSONObject org.json                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              