{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "75", "endOffsets": "277"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "23379", "endColumns": "79", "endOffsets": "23454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "92,168,169,170", "startColumns": "4,4,4,4", "startOffsets": "7952,13879,13982,14096", "endColumns": "101,102,113,100", "endOffsets": "8049,13977,14091,14192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,237,312,425,518,603,688,780,870,954,1020,1114,1206,1294,1374,1471,1552,1643,1716,1785,1865,1949,2044", "endColumns": "73,107,74,112,92,84,84,91,89,83,65,93,91,87,79,96,80,90,72,68,79,83,94,102", "endOffsets": "124,232,307,420,513,598,683,775,865,949,1015,1109,1201,1289,1369,1466,1547,1638,1711,1780,1860,1944,2039,2142"}, "to": {"startLines": "95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,205,206,207,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8202,8402,8510,8585,8698,8791,8876,8961,9053,9143,9227,9293,9387,9479,9567,9647,9744,9825,17175,17248,17317,17397,17481,17576", "endColumns": "73,107,74,112,92,84,84,91,89,83,65,93,91,87,79,96,80,90,72,68,79,83,94,102", "endOffsets": "8271,8505,8580,8693,8786,8871,8956,9048,9138,9222,9288,9382,9474,9562,9642,9739,9820,9911,17243,17312,17392,17476,17571,17674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,374,518,648,772,906,1037,1135,1270,1407", "endColumns": "105,89,122,143,129,123,133,130,97,134,136,118", "endOffsets": "156,246,369,513,643,767,901,1032,1130,1265,1402,1521"}, "to": {"startLines": "91,94,171,172,173,174,175,176,177,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7846,8112,14197,14320,14464,14594,14718,14852,14983,15081,15216,15353", "endColumns": "105,89,122,143,129,123,133,130,97,134,136,118", "endOffsets": "7947,8197,14315,14459,14589,14713,14847,14978,15076,15211,15348,15467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3610,3674", "endColumns": "63,65", "endOffsets": "3669,3735"}, "to": {"startLines": "159,160", "startColumns": "4,4", "startOffsets": "13261,13325", "endColumns": "63,65", "endOffsets": "13320,13386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3662,3725,3802,3879,3933", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,51,62,76,76,53,65", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3657,3720,3797,3874,3928,3994"}, "to": {"startLines": "2,11,17,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,148,149,150,151,152,153,154,155,156,157,158,161,162,163,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,705,9916,9997,10077,10155,10257,10355,10433,10497,10586,10678,10748,10814,10879,10951,11064,11179,11302,11376,11456,11528,11609,11703,11798,11865,12589,12642,12700,12748,12809,12875,12942,13005,13072,13137,13196,13391,13443,13506,13583,13660,13714", "endLines": "10,16,22,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,148,149,150,151,152,153,154,155,156,157,158,161,162,163,164,165,166", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,51,62,76,76,53,65", "endOffsets": "377,700,1014,9992,10072,10150,10252,10350,10428,10492,10581,10673,10743,10809,10874,10946,11059,11174,11297,11371,11451,11523,11604,11698,11793,11860,11925,12637,12695,12743,12804,12870,12937,13000,13067,13132,13191,13256,13438,13501,13578,13655,13709,13775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,210,281,352,439,507,577,656,738,824,910,980,1056,1133,1215,1296,1378,1453,1524,1594,1678,1751,1829,1900", "endColumns": "71,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "122,205,276,347,434,502,572,651,733,819,905,975,1051,1128,1210,1291,1373,1448,1519,1589,1673,1746,1824,1895,1975"}, "to": {"startLines": "56,72,181,188,189,191,211,212,213,261,262,263,264,269,270,271,272,273,274,275,276,278,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3981,5511,15472,15945,16016,16164,17679,17749,17828,21716,21802,21888,21958,22354,22431,22513,22594,22676,22751,22822,22892,23077,23150,23228,23299", "endColumns": "71,82,70,70,86,67,69,78,81,85,85,69,75,76,81,80,81,74,70,69,83,72,77,70,79", "endOffsets": "4048,5589,15538,16011,16098,16227,17744,17823,17905,21797,21883,21953,22029,22426,22508,22589,22671,22746,22817,22887,22971,23145,23223,23294,23374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5594,5700,5860,5985,6095,6248,6375,6487,6731,6906,7017,7181,7309,7470,7625,7693,7760", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "5695,5855,5980,6090,6243,6370,6482,6583,6901,7012,7176,7304,7465,7620,7688,7755,7841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "6588", "endColumns": "142", "endOffsets": "6726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1156,1221,1320,1396,1457,1546,1610,1677,1731,1799,1859,1913,2030,2090,2152,2206,2278,2400,2484,2563,2657,2740,2832,2969,3047,3129,3256,3344,3424,3478,3529,3595,3667,3744,3815,3896,3968,4045,4119,4190,4295,4383,4454,4547,4642,4716,4790,4886,4938,5021,5088,5174,5262,5324,5388,5451,5519,5629,5735,5834,5948,6006,6061,6140,6223,6298", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1151,1216,1315,1391,1452,1541,1605,1672,1726,1794,1854,1908,2025,2085,2147,2201,2273,2395,2479,2558,2652,2735,2827,2964,3042,3124,3251,3339,3419,3473,3524,3590,3662,3739,3810,3891,3963,4040,4114,4185,4290,4378,4449,4542,4637,4711,4785,4881,4933,5016,5083,5169,5257,5319,5383,5446,5514,5624,5730,5829,5943,6001,6056,6135,6218,6293,6372"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,96,97,167,187,190,192,193,194,195,196,197,198,199,200,201,202,203,204,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1019,4053,4131,4209,4286,4389,5212,5304,5430,8276,8337,13780,15869,16103,16232,16321,16385,16452,16506,16574,16634,16688,16805,16865,16927,16981,17053,17910,17994,18073,18167,18250,18342,18479,18557,18639,18766,18854,18934,18988,19039,19105,19177,19254,19325,19406,19478,19555,19629,19700,19805,19893,19964,20057,20152,20226,20300,20396,20448,20531,20598,20684,20772,20834,20898,20961,21029,21139,21245,21344,21458,21516,21571,22117,22200,22275", "endLines": "28,57,58,59,60,61,69,70,71,96,97,167,187,190,192,193,194,195,196,197,198,199,200,201,202,203,204,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,266,267,268", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "1282,4126,4204,4281,4384,4476,5299,5425,5506,8332,8397,13874,15940,16159,16316,16380,16447,16501,16569,16629,16683,16800,16860,16922,16976,17048,17170,17989,18068,18162,18245,18337,18474,18552,18634,18761,18849,18929,18983,19034,19100,19172,19249,19320,19401,19473,19550,19624,19695,19800,19888,19959,20052,20147,20221,20295,20391,20443,20526,20593,20679,20767,20829,20893,20956,21024,21134,21240,21339,21453,21511,21566,21645,22195,22270,22349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1394,1496,1606,1692,1797,1914,1992,2068,2159,2252,2347,2441,2535,2628,2723,2820,2911,3002,3086,3190,3302,3401,3507,3618,3720,3883,22034", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1389,1491,1601,1687,1792,1909,1987,2063,2154,2247,2342,2436,2530,2623,2718,2815,2906,2997,3081,3185,3297,3396,3502,3613,3715,3878,3976,22112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "62,63,64,65,66,67,68,277", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4481,4579,4681,4782,4881,4986,5093,22976", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4574,4676,4777,4876,4981,5088,5207,23072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\35a00de50ceac9bdbb8fc43812824536\\transformed\\android-image-cropper-4.6.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,113,162,234,301,385,439", "endColumns": "57,48,71,66,83,53,65", "endOffsets": "108,157,229,296,380,434,500"}, "to": {"startLines": "93,182,183,184,185,186,260", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8054,15543,15592,15664,15731,15815,21650", "endColumns": "57,48,71,66,83,53,65", "endOffsets": "8107,15587,15659,15726,15810,15864,21711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "139,140,141,142,143,144,145,146,147", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11930,12006,12068,12131,12200,12277,12347,12429,12509", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "12001,12063,12126,12195,12272,12342,12424,12504,12584"}}]}]}