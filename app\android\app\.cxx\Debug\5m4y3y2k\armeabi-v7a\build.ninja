# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen cmake_object_order_depends_target_react_codegen_lottiereactnative cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/K_/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug K$:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\K_\2025\thenextdoor\app\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IK:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\K_\2025\thenextdoor\app\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IK:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libappmodules.so

build K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o CMakeFiles/appmodules.dir/K_/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so || K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen lottiereactnative_autolinked_build/react_codegen_lottiereactnative rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = K:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so  K:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so  K:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libappmodules.so
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libappmodules.pdb
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNGoogleSignInCGen


#############################################
# Order-only phony target for react_codegen_RNGoogleSignInCGen

build cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen: phony || RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/RNGoogleSignInCGen-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\RNGoogleSignInCGen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/Props.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\RNGoogleSignInCGenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o: CXX_COMPILER__react_codegen_RNGoogleSignInCGen_Debug K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen/States.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleSignInCGen
  DEP_FILE = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir
  OBJECT_FILE_DIR = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\react\renderer\components\RNGoogleSignInCGen
  TARGET_COMPILE_PDB = RNGoogleSignInCGen_autolinked_build\CMakeFiles\react_codegen_RNGoogleSignInCGen.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNGoogleSignInCGen

build RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o


#############################################
# Utility command for edit_cache

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\RNGoogleSignInCGen_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNGoogleSignInCGen_autolinked_build/edit_cache: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNGoogleSignInCGen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\RNGoogleSignInCGen_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNGoogleSignInCGen_autolinked_build/rebuild_cache: phony RNGoogleSignInCGen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_lottiereactnative


#############################################
# Order-only phony target for react_codegen_lottiereactnative

build cmake_object_order_depends_target_react_codegen_lottiereactnative: phony || lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/lottiereactnative-generated.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\lottiereactnative-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/Props.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/States.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\lottiereactnativeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_lottiereactnative

build lottiereactnative_autolinked_build/react_codegen_lottiereactnative: phony lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o


#############################################
# Utility command for edit_cache

build lottiereactnative_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\lottiereactnative_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build lottiereactnative_autolinked_build/edit_cache: phony lottiereactnative_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build lottiereactnative_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\lottiereactnative_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build lottiereactnative_autolinked_build/rebuild_cache: phony lottiereactnative_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnreanimated_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnreanimated_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/042915bc76d6ac2c27d3f8d3378faeb7/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\042915bc76d6ac2c27d3f8d3378faeb7\renderer\components\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\042915bc76d6ac2c27d3f8d3378faeb7\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51c10b2b370fee550db69e374a355ea9/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\51c10b2b370fee550db69e374a355ea9\react\renderer\components\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\51c10b2b370fee550db69e374a355ea9\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6c8f7dc7961888c37f4488b157928c3f/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6c8f7dc7961888c37f4488b157928c3f\react\renderer\components\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6c8f7dc7961888c37f4488b157928c3f\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/eb377dec8f2080694d9e60b7a4c74137/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\eb377dec8f2080694d9e60b7a4c74137\jni\react\renderer\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\eb377dec8f2080694d9e60b7a4c74137\jni\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3d0300ff03c45d2a27990b161e444fac/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3d0300ff03c45d2a27990b161e444fac\codegen\jni\react\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3d0300ff03c45d2a27990b161e444fac\codegen\jni\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/eb377dec8f2080694d9e60b7a4c74137/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\eb377dec8f2080694d9e60b7a4c74137\jni\react\renderer\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\eb377dec8f2080694d9e60b7a4c74137\jni\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3d0300ff03c45d2a27990b161e444fac/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3d0300ff03c45d2a27990b161e444fac\codegen\jni\react\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3d0300ff03c45d2a27990b161e444fac\codegen\jni\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3941531dc7cf7b8244c0bf06a6e481bc/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3941531dc7cf7b8244c0bf06a6e481bc\components\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3941531dc7cf7b8244c0bf06a6e481bc\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e0ec770bc6d64a2de0891852087a07/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b0e0ec770bc6d64a2de0891852087a07\build\generated\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\b0e0ec770bc6d64a2de0891852087a07\build\generated\source\codegen\jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.so

build K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/042915bc76d6ac2c27d3f8d3378faeb7/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/51c10b2b370fee550db69e374a355ea9/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6c8f7dc7961888c37f4488b157928c3f/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/eb377dec8f2080694d9e60b7a4c74137/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3d0300ff03c45d2a27990b161e444fac/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/eb377dec8f2080694d9e60b7a4c74137/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3d0300ff03c45d2a27990b161e444fac/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3941531dc7cf7b8244c0bf06a6e481bc/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b0e0ec770bc6d64a2de0891852087a07/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_FILE = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_safeareacontext.pdb


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\safeareacontext_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\safeareacontext_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56ba873a1205d30e2ca9a43d21360a1/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f56ba873a1205d30e2ca9a43d21360a1\cpp\react\renderer\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f56ba873a1205d30e2ca9a43d21360a1\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/46da89650d6d6d94b3cc315e8fca21b7/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\46da89650d6d6d94b3cc315e8fca21b7\common\cpp\react\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\46da89650d6d6d94b3cc315e8fca21b7\common\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/46da89650d6d6d94b3cc315e8fca21b7/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\46da89650d6d6d94b3cc315e8fca21b7\common\cpp\react\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\46da89650d6d6d94b3cc315e8fca21b7\common\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/14a5652695bc3cb767ef6b3b96ae5af6/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\14a5652695bc3cb767ef6b3b96ae5af6\react\renderer\components\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\14a5652695bc3cb767ef6b3b96ae5af6\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56ba873a1205d30e2ca9a43d21360a1/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f56ba873a1205d30e2ca9a43d21360a1\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f56ba873a1205d30e2ca9a43d21360a1\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/14a5652695bc3cb767ef6b3b96ae5af6/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\14a5652695bc3cb767ef6b3b96ae5af6\react\renderer\components\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\14a5652695bc3cb767ef6b3b96ae5af6\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56ba873a1205d30e2ca9a43d21360a1/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f56ba873a1205d30e2ca9a43d21360a1\cpp\react\renderer\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f56ba873a1205d30e2ca9a43d21360a1\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/46da89650d6d6d94b3cc315e8fca21b7/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\46da89650d6d6d94b3cc315e8fca21b7\common\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\46da89650d6d6d94b3cc315e8fca21b7\common\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5314472a18f777a46123f4920c05c179/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5314472a18f777a46123f4920c05c179\source\codegen\jni\react\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5314472a18f777a46123f4920c05c179\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5314472a18f777a46123f4920c05c179/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5314472a18f777a46123f4920c05c179\source\codegen\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5314472a18f777a46123f4920c05c179\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ef09ceffcef29acec3e2274c63c62c12/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ef09ceffcef29acec3e2274c63c62c12\generated\source\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ef09ceffcef29acec3e2274c63c62c12\generated\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5314472a18f777a46123f4920c05c179/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5314472a18f777a46123f4920c05c179\source\codegen\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5314472a18f777a46123f4920c05c179\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ef09ceffcef29acec3e2274c63c62c12/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ef09ceffcef29acec3e2274c63c62c12\generated\source\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ef09ceffcef29acec3e2274c63c62c12\generated\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e8f47ff78939fad81832d66c1b894788/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\e8f47ff78939fad81832d66c1b894788\codegen\jni\react\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\e8f47ff78939fad81832d66c1b894788\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.so

build K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56ba873a1205d30e2ca9a43d21360a1/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/46da89650d6d6d94b3cc315e8fca21b7/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/46da89650d6d6d94b3cc315e8fca21b7/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/14a5652695bc3cb767ef6b3b96ae5af6/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56ba873a1205d30e2ca9a43d21360a1/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/14a5652695bc3cb767ef6b3b96ae5af6/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f56ba873a1205d30e2ca9a43d21360a1/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/46da89650d6d6d94b3cc315e8fca21b7/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5314472a18f777a46123f4920c05c179/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5314472a18f777a46123f4920c05c179/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ef09ceffcef29acec3e2274c63c62c12/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5314472a18f777a46123f4920c05c179/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ef09ceffcef29acec3e2274c63c62c12/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e8f47ff78939fad81832d66c1b894788/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_FILE = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.so
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnscreens.pdb


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnscreens_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnscreens_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6782b29b0994d4f25e09133cec8cf295/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\6782b29b0994d4f25e09133cec8cf295\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\6782b29b0994d4f25e09133cec8cf295\react-native-svg\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/K_/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\K_\2025\thenextdoor\app\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\K_\2025\thenextdoor\app\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0d9a905e66dfea013dacf25ba7f101aa/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0d9a905e66dfea013dacf25ba7f101aa\common\cpp\react\renderer\components\rnsvg\RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0d9a905e66dfea013dacf25ba7f101aa\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/K_/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\K_\2025\thenextdoor\app\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg\RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\K_\2025\thenextdoor\app\node_modules\react-native-svg\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9e11501fc9699a406d22b1b85b46a5a9/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9e11501fc9699a406d22b1b85b46a5a9\generated\source\codegen\jni\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9e11501fc9699a406d22b1b85b46a5a9\generated\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1e0d5b47fcdfcd3b47d12d0e72a2de81/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1e0d5b47fcdfcd3b47d12d0e72a2de81\build\generated\source\codegen\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1e0d5b47fcdfcd3b47d12d0e72a2de81\build\generated\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0d9a905e66dfea013dacf25ba7f101aa/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0d9a905e66dfea013dacf25ba7f101aa\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0d9a905e66dfea013dacf25ba7f101aa\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1e0d5b47fcdfcd3b47d12d0e72a2de81/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1e0d5b47fcdfcd3b47d12d0e72a2de81\build\generated\source\codegen\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\1e0d5b47fcdfcd3b47d12d0e72a2de81\build\generated\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0d9a905e66dfea013dacf25ba7f101aa/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0d9a905e66dfea013dacf25ba7f101aa\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0d9a905e66dfea013dacf25ba7f101aa\android\build\generated\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9e11501fc9699a406d22b1b85b46a5a9/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9e11501fc9699a406d22b1b85b46a5a9\generated\source\codegen\jni\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IK:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9e11501fc9699a406d22b1b85b46a5a9\generated\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.so

build K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_Debug rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6782b29b0994d4f25e09133cec8cf295/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/K_/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0d9a905e66dfea013dacf25ba7f101aa/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/K_/2025/thenextdoor/app/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9e11501fc9699a406d22b1b85b46a5a9/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1e0d5b47fcdfcd3b47d12d0e72a2de81/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0d9a905e66dfea013dacf25ba7f101aa/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1e0d5b47fcdfcd3b47d12d0e72a2de81/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0d9a905e66dfea013dacf25ba7f101aa/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9e11501fc9699a406d22b1b85b46a5a9/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_FILE = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.so
  TARGET_PDB = K:\2025\thenextdoor\app\android\app\build\intermediates\cxx\Debug\5m4y3y2k\obj\armeabi-v7a\libreact_codegen_rnsvg.pdb


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnsvg_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\rnsvg_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# K:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IK:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\RNCWebViewSpec_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a\RNCWebViewSpec_autolinked_build && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BK:\2025\thenextdoor\app\android\app\.cxx\Debug\5m4y3y2k\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libappmodules.so

build libappmodules.so: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libappmodules.so

build libreact_codegen_rnscreens.so: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_RNGoogleSignInCGen: phony RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen

build react_codegen_lottiereactnative: phony lottiereactnative_autolinked_build/react_codegen_lottiereactnative

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a

build all: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libappmodules.so rnasyncstorage_autolinked_build/all RNGoogleSignInCGen_autolinked_build/all lottiereactnative_autolinked_build/all rngesturehandler_codegen_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all RNCWebViewSpec_autolinked_build/all

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/RNGoogleSignInCGen_autolinked_build

build RNGoogleSignInCGen_autolinked_build/all: phony RNGoogleSignInCGen_autolinked_build/react_codegen_RNGoogleSignInCGen

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/lottiereactnative_autolinked_build

build lottiereactnative_autolinked_build/all: phony lottiereactnative_autolinked_build/react_codegen_lottiereactnative

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony K$:/2025/thenextdoor/app/android/app/build/intermediates/cxx/Debug/5m4y3y2k/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/CMakeFiles/cmake.verify_globs | C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake K$:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake K$:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake K$:/2025/thenextdoor/app/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake K$:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
