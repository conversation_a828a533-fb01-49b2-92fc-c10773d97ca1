1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.UNextDoor.app"
4    android:versionCode="1"
5    android:versionName="1.0.2" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:3-75
11-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:3-78
12-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:3-76
13-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:3-76
14-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.BLUETOOTH" />
15-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:3-65
15-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:20-63
16    <uses-permission android:name="android.permission.CAMERA" />
16-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:3-62
16-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:20-60
17    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
17-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:3-76
17-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:20-74
18    <uses-permission android:name="android.permission.INTERNET" />
18-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:3-64
18-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:20-62
19    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
19-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:3-77
19-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:20-75
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:3-77
20-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:20-75
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:3-68
21-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:20-66
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:3-63
22-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:20-61
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:3-65
23-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:20-63
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:15:3-78
24-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:15:20-76
25    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
25-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:16:3-70
25-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:16:20-68
26
27    <queries>
27-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:3-23:13
28        <intent>
28-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:5-22:14
29            <action android:name="android.intent.action.VIEW" />
29-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
29-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
30
31            <category android:name="android.intent.category.BROWSABLE" />
31-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:7-67
31-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:17-65
32
33            <data android:scheme="https" />
33-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
33-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
34        </intent>
35        <intent>
35-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
36            <action android:name="org.chromium.intent.action.PAY" />
36-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
36-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
37        </intent>
38        <intent>
38-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
39            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
39-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
39-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
40        </intent>
41        <intent>
41-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
42            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
42-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
42-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
43        </intent>
44
45        <package android:name="host.exp.exponent" />
45-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
45-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
46
47        <intent>
47-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
48
49            <!-- Required for picking images from the camera roll if targeting API 30 -->
50            <action android:name="android.media.action.IMAGE_CAPTURE" />
50-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
50-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
51        </intent>
52        <intent>
52-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
53
54            <!-- Required for picking images from the camera if targeting API 30 -->
55            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
55-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
56        </intent>
57        <intent>
57-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
58            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
58-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
58-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
59        </intent>
60        <intent>
60-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
61            <action android:name="android.intent.action.GET_CONTENT" />
61-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
61-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
62
63            <category android:name="android.intent.category.OPENABLE" />
63-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
63-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
64
65            <data android:mimeType="*/*" />
65-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
66        </intent> <!-- Query open documents -->
67        <intent>
67-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
68            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
68-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
68-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
69        </intent>
70        <intent>
70-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:8:9-12:18
71
72            <!-- Required for text-to-speech if targeting API 30 -->
73            <action android:name="android.intent.action.TTS_SERVICE" />
73-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:13-72
73-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:21-69
74        </intent>
75        <intent>
75-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
76            <action android:name="android.intent.action.VIEW" />
76-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
76-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
77
78            <data
78-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
79                android:mimeType="*/*"
80                android:scheme="*" />
80-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
81        </intent>
82        <intent>
82-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
83            <action android:name="android.intent.action.VIEW" />
83-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
83-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
84
85            <category android:name="android.intent.category.BROWSABLE" />
85-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:7-67
85-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:17-65
86
87            <data
87-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
88                android:host="pay"
89                android:mimeType="*/*"
90                android:scheme="upi" />
90-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
91        </intent>
92        <intent>
92-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:28:9-30:18
93            <action android:name="android.intent.action.MAIN" />
93-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:9-60
93-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:17-58
94        </intent>
95        <intent>
95-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
96            <action android:name="android.intent.action.SEND" />
96-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:32:13-65
96-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:32:21-62
97
98            <data android:mimeType="*/*" />
98-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
99        </intent>
100        <intent>
100-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
101            <action android:name="rzp.device_token.share" />
101-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:37:13-61
101-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:37:21-58
102        </intent>
103    </queries>
104
105    <permission android:name="android.permission.INTERNET" />
105-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:3-59
105-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:15-57
106
107    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
107-->[:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
107-->[:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
108    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
108-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:5-77
108-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:22-74
109    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
109-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:5-92
109-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:22-89
110    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
110-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
110-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
111    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
111-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
111-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
112    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
112-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:5-76
112-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:22-73
113    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
113-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:5-75
113-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:22-72
114    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
114-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:5-75
114-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:22-72
115    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
115-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:5-90
115-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:22-87
116    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
116-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
116-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
117    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
117-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
117-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
118    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
118-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
118-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
119
120    <uses-feature
120-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
121        android:glEsVersion="0x00020000"
121-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
122        android:required="true" />
122-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
123
124    <permission
124-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
125        android:name="com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
125-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
126        android:protectionLevel="signature" />
126-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
127
128    <uses-permission android:name="com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
128-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
128-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
129    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
129-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
129-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
130    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
131    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
132    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
133    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
134    <!-- for Samsung -->
135    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
136    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
137    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
138    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
139    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
140    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
141    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
142    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
143    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
144    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
145    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
146    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
146-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
146-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
147    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
147-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
147-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
148    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
148-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
148-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
149    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
149-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
149-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
150    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
150-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
150-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
151
152    <application
152-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:3-43:17
153        android:name="com.UNextDoor.app.MainApplication"
153-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:16-47
154        android:allowBackup="true"
154-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:162-188
155        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
155-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
156        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
156-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:351-420
157        android:debuggable="true"
158        android:extractNativeLibs="false"
159        android:fullBackupContent="@xml/secure_store_backup_rules"
159-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:292-350
160        android:icon="@mipmap/ic_launcher"
160-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:81-115
161        android:label="@string/app_name"
161-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:48-80
162        android:requestLegacyExternalStorage="true"
162-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:248-291
163        android:roundIcon="@mipmap/ic_launcher_round"
163-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:116-161
164        android:supportsRtl="true"
164-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:221-247
165        android:theme="@style/AppTheme"
165-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:189-220
166        android:usesCleartextTraffic="true" >
166-->K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:18-53
167        <meta-data
167-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:5-83
168            android:name="expo.modules.updates.ENABLED"
168-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:16-59
169            android:value="false" />
169-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:60-81
170        <meta-data
170-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:5-119
171            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
171-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:16-72
172            android:value="@string/expo_runtime_version" />
172-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:73-117
173        <meta-data
173-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:5-105
174            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
174-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:16-80
175            android:value="ALWAYS" />
175-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:81-103
176        <meta-data
176-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:5-99
177            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
177-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:16-79
178            android:value="0" />
178-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:80-97
179
180        <activity
180-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:5-42:16
181            android:name="com.UNextDoor.app.MainActivity"
181-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:15-43
182            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
182-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:44-134
183            android:exported="true"
183-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:253-276
184            android:launchMode="singleTask"
184-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:135-166
185            android:screenOrientation="portrait"
185-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:277-313
186            android:theme="@style/Theme.App.SplashScreen"
186-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:207-252
187            android:windowSoftInputMode="adjustPan" >
187-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:167-206
188            <intent-filter>
188-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:31:7-34:23
189                <action android:name="android.intent.action.MAIN" />
189-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:9-60
189-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:17-58
190
191                <category android:name="android.intent.category.LAUNCHER" />
191-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:33:9-68
191-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:33:19-66
192            </intent-filter>
193            <intent-filter>
193-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:35:7-41:23
194                <action android:name="android.intent.action.VIEW" />
194-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
194-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
195
196                <category android:name="android.intent.category.DEFAULT" />
196-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:9-67
196-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:19-65
197                <category android:name="android.intent.category.BROWSABLE" />
197-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:7-67
197-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:17-65
198
199                <data android:scheme="UNextDoor" />
199-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
199-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
200                <data android:scheme="exp+unextdoor" />
200-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
200-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
201            </intent-filter>
202        </activity>
203
204        <provider
204-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
205            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
205-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
206            android:authorities="com.UNextDoor.app.fileprovider"
206-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
207            android:exported="false"
207-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
208            android:grantUriPermissions="true" >
208-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
209            <meta-data
209-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
210                android:name="android.support.FILE_PROVIDER_PATHS"
210-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
211                android:resource="@xml/file_provider_paths" />
211-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
212        </provider>
213
214        <activity
214-->[:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
215            android:name="com.razorpay.CheckoutActivity"
215-->[:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
216            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
216-->[:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
217            android:exported="false"
217-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:45:13-37
218            android:theme="@style/CheckoutTheme" >
218-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:46:13-49
219            <intent-filter>
219-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
220                <action android:name="android.intent.action.MAIN" />
220-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:9-60
220-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:17-58
221            </intent-filter>
222        </activity>
223
224        <service
224-->[:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-12:19
225            android:name="com.oney.WebRTCModule.MediaProjectionService"
225-->[:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
226            android:foregroundServiceType="mediaProjection" >
226-->[:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-60
227        </service>
228
229        <activity
229-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
230            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
230-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
231            android:exported="true"
231-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
232            android:launchMode="singleTask"
232-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
233            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
233-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
234            <intent-filter>
234-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
235                <action android:name="android.intent.action.VIEW" />
235-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
235-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
236
237                <category android:name="android.intent.category.DEFAULT" />
237-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:9-67
237-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:19-65
238                <category android:name="android.intent.category.BROWSABLE" />
238-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:7-67
238-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:17-65
239
240                <data android:scheme="expo-dev-launcher" />
240-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
240-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
241            </intent-filter>
242        </activity>
243        <activity
243-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
244            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
244-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
245            android:screenOrientation="portrait"
245-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
246            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
246-->[:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
247        <activity
247-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
248            android:name="expo.modules.devmenu.DevMenuActivity"
248-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
249            android:exported="true"
249-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
250            android:launchMode="singleTask"
250-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
251            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
251-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
252            <intent-filter>
252-->[:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
253                <action android:name="android.intent.action.VIEW" />
253-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
253-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
254
255                <category android:name="android.intent.category.DEFAULT" />
255-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:9-67
255-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:19-65
256                <category android:name="android.intent.category.BROWSABLE" />
256-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:7-67
256-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:17-65
257
258                <data android:scheme="expo-dev-menu" />
258-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
258-->K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
259            </intent-filter>
260        </activity>
261
262        <meta-data
262-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
263            android:name="org.unimodules.core.AppLoader#react-native-headless"
263-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
264            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
264-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
265        <meta-data
265-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
266            android:name="com.facebook.soloader.enabled"
266-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
267            android:value="true" />
267-->[:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
268
269        <activity
269-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:12:9-16:49
270            android:name="expo.modules.video.FullscreenPlayerActivity"
270-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:13:13-71
271            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
271-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:14:13-91
272            android:supportsPictureInPicture="true"
272-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:15:13-52
273            android:theme="@style/Fullscreen" />
273-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:16:13-46
274
275        <service
275-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:18:9-25:19
276            android:name="expo.modules.video.playbackService.ExpoVideoPlaybackService"
276-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:19:13-87
277            android:exported="false"
277-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:20:13-37
278            android:foregroundServiceType="mediaPlayback" >
278-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:21:13-58
279            <intent-filter>
279-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:22:13-24:29
280                <action android:name="androidx.media3.session.MediaSessionService" />
280-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:17-86
280-->[host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:25-83
281            </intent-filter>
282        </service>
283
284        <activity
284-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
285            android:name="com.facebook.react.devsupport.DevSettingsActivity"
285-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
286            android:exported="false" />
286-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
287
288        <meta-data
288-->[host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:11:9-13:42
289            android:name="com.google.mlkit.vision.DEPENDENCIES"
289-->[host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:12:13-64
290            android:value="barcode_ui" />
290-->[host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:13:13-39
291
292        <service
292-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
293            android:name="com.google.android.gms.metadata.ModuleDependencies"
293-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
294            android:enabled="false"
294-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
295            android:exported="false" >
295-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
296            <intent-filter>
296-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
297                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
297-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
297-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
298            </intent-filter>
299
300            <meta-data
300-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
301                android:name="photopicker_activity:0:required"
301-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
302                android:value="" />
302-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
303        </service>
304
305        <activity
305-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
306            android:name="com.canhub.cropper.CropImageActivity"
306-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
307            android:exported="true"
307-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
308            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
308-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
309        <provider
309-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
310            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
310-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
311            android:authorities="com.UNextDoor.app.ImagePickerFileProvider"
311-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
312            android:exported="false"
312-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
313            android:grantUriPermissions="true" >
313-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
314            <meta-data
314-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
315                android:name="android.support.FILE_PROVIDER_PATHS"
315-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
316                android:resource="@xml/image_picker_provider_paths" />
316-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
317        </provider>
318
319        <uses-library
319-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
320            android:name="androidx.camera.extensions.impl"
320-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
321            android:required="false" />
321-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
322
323        <service
323-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
324            android:name="androidx.camera.core.impl.MetadataHolderService"
324-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
325            android:enabled="false"
325-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
326            android:exported="false" >
326-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
327            <meta-data
327-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
328                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
328-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
329                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
329-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
330        </service>
331
332        <provider
332-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
333            android:name="com.canhub.cropper.CropFileProvider"
333-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
334            android:authorities="com.UNextDoor.app.cropper.fileprovider"
334-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
335            android:exported="false"
335-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
336            android:grantUriPermissions="true" >
336-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
337            <meta-data
337-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
338                android:name="android.support.FILE_PROVIDER_PATHS"
338-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
339                android:resource="@xml/library_file_paths" />
339-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
340        </provider>
341        <provider
341-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
342            android:name="expo.modules.filesystem.FileSystemFileProvider"
342-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
343            android:authorities="com.UNextDoor.app.FileSystemFileProvider"
343-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
344            android:exported="false"
344-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
345            android:grantUriPermissions="true" >
345-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
346            <meta-data
346-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
347                android:name="android.support.FILE_PROVIDER_PATHS"
347-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
348                android:resource="@xml/file_system_provider_paths" />
348-->[:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
349        </provider>
350
351        <service
351-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:11:9-14:56
352            android:name="expo.modules.location.services.LocationTaskService"
352-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:12:13-78
353            android:exported="false"
353-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:13:13-37
354            android:foregroundServiceType="location" />
354-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:14:13-53
355        <service
355-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
356            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
356-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
357            android:exported="false" >
357-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
358            <intent-filter android:priority="-1" >
358-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
358-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
359                <action android:name="com.google.firebase.MESSAGING_EVENT" />
359-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
359-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
360            </intent-filter>
361        </service>
362
363        <receiver
363-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
364            android:name="expo.modules.notifications.service.NotificationsService"
364-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
365            android:enabled="true"
365-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
366            android:exported="false" >
366-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
367            <intent-filter android:priority="-1" >
367-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
367-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
368                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
368-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
368-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
369                <action android:name="android.intent.action.BOOT_COMPLETED" />
369-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
369-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
370                <action android:name="android.intent.action.REBOOT" />
370-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
370-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
371                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
371-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
371-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
372                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
372-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
372-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
373                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
373-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
373-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
374            </intent-filter>
375        </receiver>
376
377        <activity
377-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
378            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
378-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
379            android:excludeFromRecents="true"
379-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
380            android:exported="false"
380-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
381            android:launchMode="standard"
381-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
382            android:noHistory="true"
382-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
383            android:taskAffinity=""
383-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
384            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
384-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
385
386        <provider
386-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
387            android:name="androidx.startup.InitializationProvider"
387-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:53:13-67
388            android:authorities="com.UNextDoor.app.androidx-startup"
388-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:54:13-68
389            android:exported="false" >
389-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:55:13-37
390            <meta-data
390-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
391                android:name="com.razorpay.RazorpayInitializer"
391-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:58:17-64
392                android:value="androidx.startup" />
392-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:59:17-49
393            <meta-data
393-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
394                android:name="androidx.emoji2.text.EmojiCompatInitializer"
394-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
395                android:value="androidx.startup" />
395-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
396            <meta-data
396-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
397                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
397-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
398                android:value="androidx.startup" />
398-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
399            <meta-data
399-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
400                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
400-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
401                android:value="androidx.startup" />
401-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
402        </provider>
403
404        <activity
404-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
405            android:name="com.razorpay.MagicXActivity"
405-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:63:13-55
406            android:exported="false"
406-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:64:13-37
407            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
407-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:65:13-72
408
409        <meta-data
409-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
410            android:name="com.razorpay.plugin.googlepay_all"
410-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:68:13-61
411            android:value="com.razorpay.RzpGpayMerged" />
411-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:69:13-55
412
413        <receiver
413-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
414            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
414-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
415            android:exported="true"
415-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
416            android:permission="com.google.android.c2dm.permission.SEND" >
416-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
417            <intent-filter>
417-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
418                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
418-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
418-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
419            </intent-filter>
420
421            <meta-data
421-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
422                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
422-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
423                android:value="true" />
423-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
424        </receiver>
425        <!--
426             FirebaseMessagingService performs security checks at runtime,
427             but set to not exported to explicitly avoid allowing another app to call it.
428        -->
429        <service
429-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
430            android:name="com.google.firebase.messaging.FirebaseMessagingService"
430-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
431            android:directBootAware="true"
431-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
432            android:exported="false" >
432-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
433            <intent-filter android:priority="-500" >
433-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
433-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
434                <action android:name="com.google.firebase.MESSAGING_EVENT" />
434-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
434-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
435            </intent-filter>
436        </service>
437        <service
437-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
438            android:name="com.google.firebase.components.ComponentDiscoveryService"
438-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
439            android:directBootAware="true"
439-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
440            android:exported="false" >
440-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
441            <meta-data
441-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
442                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
442-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
443                android:value="com.google.firebase.components.ComponentRegistrar" />
443-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
444            <meta-data
444-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
445                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
445-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
446                android:value="com.google.firebase.components.ComponentRegistrar" />
446-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
447            <meta-data
447-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
448                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
448-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
449                android:value="com.google.firebase.components.ComponentRegistrar" />
449-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
450            <meta-data
450-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
451                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
451-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
452                android:value="com.google.firebase.components.ComponentRegistrar" />
452-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
453            <meta-data
453-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
454                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
454-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
455                android:value="com.google.firebase.components.ComponentRegistrar" />
455-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
456            <meta-data
456-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
457                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
457-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
458                android:value="com.google.firebase.components.ComponentRegistrar" />
458-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
459            <meta-data
459-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
460                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
460-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
461                android:value="com.google.firebase.components.ComponentRegistrar" />
461-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
462        </service>
463
464        <activity
464-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
465            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
465-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
466            android:excludeFromRecents="true"
466-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
467            android:exported="false"
467-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
468            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
468-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
469        <!--
470            Service handling Google Sign-In user revocation. For apps that do not integrate with
471            Google Sign-In, this service will never be started.
472        -->
473        <service
473-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
474            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
474-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
475            android:exported="true"
475-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
476            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
476-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
477            android:visibleToInstantApps="true" />
477-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
478        <!--
479        This activity is an invisible delegate activity to start scanner activity
480        and receive result, so it's unnecessary to support screen orientation and
481        we can avoid any side effect from activity recreation in any case.
482        -->
483        <activity
483-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
484            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
484-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
485            android:exported="false"
485-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
486            android:screenOrientation="portrait" >
486-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
487        </activity>
488
489        <service
489-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
490            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
490-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
491            android:directBootAware="true"
491-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
492            android:exported="false" >
492-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
493            <meta-data
493-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
494                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
494-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
495                android:value="com.google.firebase.components.ComponentRegistrar" />
495-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
496            <meta-data
496-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
497                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
497-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
498                android:value="com.google.firebase.components.ComponentRegistrar" />
498-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
499            <meta-data
499-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
500                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
500-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
501                android:value="com.google.firebase.components.ComponentRegistrar" />
501-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
502        </service>
503
504        <provider
504-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
505            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
505-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
506            android:authorities="com.UNextDoor.app.mlkitinitprovider"
506-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
507            android:exported="false"
507-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
508            android:initOrder="99" /> <!-- Needs to be explicitly declared on P+ -->
508-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
509        <uses-library
509-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
510            android:name="org.apache.http.legacy"
510-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
511            android:required="false" />
511-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
512
513        <activity
513-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
514            android:name="com.google.android.gms.common.api.GoogleApiActivity"
514-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
515            android:exported="false"
515-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
516            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
516-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
517
518        <provider
518-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
519            android:name="com.google.firebase.provider.FirebaseInitProvider"
519-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
520            android:authorities="com.UNextDoor.app.firebaseinitprovider"
520-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
521            android:directBootAware="true"
521-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
522            android:exported="false"
522-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
523            android:initOrder="100" />
523-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
524
525        <meta-data
525-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
526            android:name="com.google.android.gms.version"
526-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
527            android:value="@integer/google_play_services_version" />
527-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
528
529        <receiver
529-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
530            android:name="androidx.profileinstaller.ProfileInstallReceiver"
530-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
531            android:directBootAware="false"
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
532            android:enabled="true"
532-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
533            android:exported="true"
533-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
534            android:permission="android.permission.DUMP" >
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
535            <intent-filter>
535-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
536                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
536-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
536-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
537            </intent-filter>
538            <intent-filter>
538-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
539                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
539-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
539-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
540            </intent-filter>
541            <intent-filter>
541-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
542                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
542-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
542-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
543            </intent-filter>
544            <intent-filter>
544-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
545                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
545-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
545-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
546            </intent-filter>
547        </receiver>
548
549        <service
549-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
550            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
550-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
551            android:exported="false" >
551-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
552            <meta-data
552-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
553                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
553-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
554                android:value="cct" />
554-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
555        </service>
556        <service
556-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
557            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
557-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
558            android:exported="false"
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
559            android:permission="android.permission.BIND_JOB_SERVICE" >
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
560        </service>
561
562        <receiver
562-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
563            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
563-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
564            android:exported="false" />
564-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
565    </application>
566
567</manifest>
