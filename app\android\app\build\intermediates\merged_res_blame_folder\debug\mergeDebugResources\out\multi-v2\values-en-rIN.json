{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "54,126,127,128", "startColumns": "4,4,4,4", "startOffsets": "4188,9612,9709,9818", "endColumns": "97,96,108,98", "endOffsets": "4281,9704,9813,9912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7784,7854,7916,7981,8045,8122,8187,8277,8362", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "7849,7911,7976,8040,8117,8182,8272,8357,8426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3223,3287", "endColumns": "63,65", "endOffsets": "3282,3348"}, "to": {"startLines": "118,119", "startColumns": "4,4", "startOffsets": "9096,9160", "endColumns": "63,65", "endOffsets": "9155,9221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "46,47,48,49,50,51,52,146", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3561,3660,3759,3863,3966,11723", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3454,3556,3655,3754,3858,3961,4077,11819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,291,387,482,565,649,731,817,902,966,1061,1141,1235,1309,1400,1472,1561,1629,1695,1768,1850,1940", "endColumns": "73,88,72,95,94,82,83,81,85,84,63,94,79,93,73,90,71,88,67,65,72,81,89,96", "endOffsets": "124,213,286,382,477,560,644,726,812,897,961,1056,1136,1230,1304,1395,1467,1556,1624,1690,1763,1845,1935,2032"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4376,4450,4539,4612,4708,4803,4886,4970,5052,5138,5223,5287,5382,5462,5556,5630,5721,5793,11164,11232,11298,11371,11453,11543", "endColumns": "73,88,72,95,94,82,83,81,85,84,63,94,79,93,73,90,71,88,67,65,72,81,89,96", "endOffsets": "4445,4534,4607,4703,4798,4881,4965,5047,5133,5218,5282,5377,5457,5551,5625,5716,5788,5877,11227,11293,11366,11448,11538,11635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "53,55,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4082,4286,9917,10036,10165,10303,10430,10548,10679,10779,10905,11044", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "4183,4371,10031,10160,10298,10425,10543,10674,10774,10900,11039,11159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,810,910,1018,1102,1202,1317,1395,1470,1561,1654,1749,1843,1943,2036,2131,2225,2316,2407,2489,2592,2695,2794,2899,3003,3107,3263,11640", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "805,905,1013,1097,1197,1312,1390,1465,1556,1649,1744,1838,1938,2031,2126,2220,2311,2402,2484,2587,2690,2789,2894,2998,3102,3258,3358,11718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3275,3337,3413,3489,3543", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,51,61,75,75,53,65", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3270,3332,3408,3484,3538,3604"}, "to": {"startLines": "2,11,15,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,5882,5964,6046,6124,6211,6296,6363,6426,6518,6610,6675,6738,6800,6871,6981,7092,7202,7269,7349,7420,7487,7572,7657,7720,8431,8484,8542,8590,8651,8716,8778,8843,8914,8972,9030,9226,9278,9340,9416,9492,9546", "endLines": "10,14,18,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,120,121,122,123,124,125", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,51,61,75,75,53,65", "endOffsets": "330,524,701,5959,6041,6119,6206,6291,6358,6421,6513,6605,6670,6733,6795,6866,6976,7087,7197,7264,7344,7415,7482,7567,7652,7715,7779,8479,8537,8585,8646,8711,8773,8838,8909,8967,9025,9091,9273,9335,9411,9487,9541,9607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-en-rIN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "206", "endColumns": "70", "endOffsets": "276"}, "to": {"startLines": "147", "startColumns": "4", "startOffsets": "11824", "endColumns": "74", "endOffsets": "11894"}}]}]}