{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,726,823,914,998,1092,1187,1259,1330,1429,1529,1596,1660,1726,1806,1924,2048,2166,2241,2333,2407,2480,2574,2662,2725,2794,2847,2905,2957,3018,3078,3140,3205,3273,3343,3402,3470,3524,3592,3679,3766,3821", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,53,67,86,86,54,66", "endOffsets": "288,517,721,818,909,993,1087,1182,1254,1325,1424,1524,1591,1655,1721,1801,1919,2043,2161,2236,2328,2402,2475,2569,2657,2720,2789,2842,2900,2952,3013,3073,3135,3200,3268,3338,3397,3465,3519,3587,3674,3761,3816,3883"}, "to": {"startLines": "2,11,15,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,617,9544,9641,9732,9816,9910,10005,10077,10148,10247,10347,10414,10478,10544,10624,10742,10866,10984,11059,11151,11225,11298,11392,11480,11543,12267,12320,12378,12430,12491,12551,12613,12678,12746,12816,12875,13078,13132,13200,13287,13374,13429", "endLines": "10,14,18,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,53,67,86,86,54,66", "endOffsets": "383,612,816,9636,9727,9811,9905,10000,10072,10143,10242,10342,10409,10473,10539,10619,10737,10861,10979,11054,11146,11220,11293,11387,11475,11538,11607,12315,12373,12425,12486,12546,12608,12673,12741,12811,12870,12938,13127,13195,13282,13369,13424,13491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3470,3537", "endColumns": "66,67", "endOffsets": "3532,3600"}, "to": {"startLines": "150,151", "startColumns": "4,4", "startOffsets": "12943,13010", "endColumns": "66,67", "endOffsets": "13005,13073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "992,1100,1207,1319,1407,1510,1625,1704,1781,1872,1965,2060,2154,2254,2347,2442,2536,2627,2720,2801,2905,3008,3106,3213,3320,3425,3582,20745", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "1095,1202,1314,1402,1505,1620,1699,1776,1867,1960,2055,2149,2249,2342,2437,2531,2622,2715,2796,2900,3003,3101,3208,3315,3420,3577,3673,20822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6198", "endColumns": "131", "endOffsets": "6325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,264,387,543,680,833,965,1109,1203,1353,1499", "endColumns": "113,94,122,155,136,152,131,143,93,149,145,121", "endOffsets": "164,259,382,538,675,828,960,1104,1198,1348,1494,1616"}, "to": {"startLines": "83,85,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7426,7652,13931,14054,14210,14347,14500,14632,14776,14870,15020,15166", "endColumns": "113,94,122,155,136,152,131,143,93,149,145,121", "endOffsets": "7535,7742,14049,14205,14342,14495,14627,14771,14865,15015,15161,15283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1075,1142,1245,1320,1383,1475,1546,1611,1678,1750,1822,1876,1997,2056,2120,2174,2251,2383,2468,2545,2635,2715,2796,2945,3032,3115,3257,3349,3427,3483,3541,3607,3679,3756,3827,3910,3990,4069,4144,4223,4327,4417,4490,4584,4681,4755,4828,4927,4982,5066,5134,5222,5311,5373,5437,5500,5571,5680,5791,5894,6002,6062,6124,6206,6289,6365", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "266,344,421,498,592,680,792,918,999,1070,1137,1240,1315,1378,1470,1541,1606,1673,1745,1817,1871,1992,2051,2115,2169,2246,2378,2463,2540,2630,2710,2791,2940,3027,3110,3252,3344,3422,3478,3536,3602,3674,3751,3822,3905,3985,4064,4139,4218,4322,4412,4485,4579,4676,4750,4823,4922,4977,5061,5129,5217,5306,5368,5432,5495,5566,5675,5786,5889,5997,6057,6119,6201,6284,6360,6443"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,87,88,158,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,240,241,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "821,3678,3756,3833,3910,4004,4822,4934,5060,7824,7895,13496,15288,15363,15426,15518,15589,15654,15721,15793,15865,15919,16040,16099,16163,16217,16294,16922,17007,17084,17174,17254,17335,17484,17571,17654,17796,17888,17966,18022,18080,18146,18218,18295,18366,18449,18529,18608,18683,18762,18866,18956,19029,19123,19220,19294,19367,19466,19521,19605,19673,19761,19850,19912,19976,20039,20110,20219,20330,20433,20541,20601,20663,20827,20910,20986", "endLines": "22,50,51,52,53,54,62,63,64,87,88,158,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,240,241,242", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "987,3751,3828,3905,3999,4087,4929,5055,5136,7890,7957,13594,15358,15421,15513,15584,15649,15716,15788,15860,15914,16035,16094,16158,16212,16289,16421,17002,17079,17169,17249,17330,17479,17566,17649,17791,17883,17961,18017,18075,18141,18213,18290,18361,18444,18524,18603,18678,18757,18861,18951,19024,19118,19215,19289,19362,19461,19516,19600,19668,19756,19845,19907,19971,20034,20105,20214,20325,20428,20536,20596,20658,20740,20905,20981,21064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,183,245,314,391,471,560,641", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "119,178,240,309,386,466,555,636,705"}, "to": {"startLines": "130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11612,11681,11740,11802,11871,11948,12028,12117,12198", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "11676,11735,11797,11866,11943,12023,12112,12193,12262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "55,56,57,58,59,60,61,243", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4092,4190,4294,4393,4496,4602,4709,21069", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "4185,4289,4388,4491,4597,4704,4817,21165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "84,159,160,161", "startColumns": "4,4,4,4", "startOffsets": "7540,13599,13707,13819", "endColumns": "111,107,111,111", "endOffsets": "7647,13702,13814,13926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5141,5252,5428,5563,5667,5835,5964,6088,6330,6490,6600,6765,6896,7054,7211,7272,7341", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "5247,5423,5558,5662,5830,5959,6083,6193,6485,6595,6760,6891,7049,7206,7267,7336,7421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,236,319,424,523,607,698,796,886,977,1053,1161,1247,1345,1431,1534,1610,1714,1784,1851,1934,2021,2111", "endColumns": "76,103,82,104,98,83,90,97,89,90,75,107,85,97,85,102,75,103,69,66,82,86,89,98", "endOffsets": "127,231,314,419,518,602,693,791,881,972,1048,1156,1242,1340,1426,1529,1605,1709,1779,1846,1929,2016,2106,2205"}, "to": {"startLines": "86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7747,7962,8066,8149,8254,8353,8437,8528,8626,8716,8807,8883,8991,9077,9175,9261,9364,9440,16426,16496,16563,16646,16733,16823", "endColumns": "76,103,82,104,98,83,90,97,89,90,75,107,85,97,85,102,75,103,69,66,82,86,89,98", "endOffsets": "7819,8061,8144,8249,8348,8432,8523,8621,8711,8802,8878,8986,9072,9170,9256,9359,9435,9539,16491,16558,16641,16728,16818,16917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "72", "endOffsets": "274"}, "to": {"startLines": "244", "startColumns": "4", "startOffsets": "21170", "endColumns": "76", "endOffsets": "21242"}}]}]}