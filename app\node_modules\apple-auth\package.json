{"name": "apple-auth", "version": "1.0.9", "description": "Sign in with Apple for NodeJS", "main": "src/apple-auth.js", "types": "src/apple-auth.d.ts", "scripts": {"dev": "nodemon src/apple-auth.js", "start": "node src/apple-auth.js"}, "repository": {"type": "git", "url": "git+https://github.com/ananay/apple-auth.git"}, "keywords": ["apple", "sign", "in", "sso", "auth", "authentication"], "author": "<PERSON><PERSON> <<EMAIL>> (https://ananayarora.com)", "license": "MIT", "dependencies": {"axios": "^0.21.1", "express": "^4.17.1", "jsonwebtoken": "^9.0.0"}, "bugs": {"url": "https://github.com/ananay/apple-auth/issues"}, "homepage": "https://github.com/ananay/apple-auth#readme"}