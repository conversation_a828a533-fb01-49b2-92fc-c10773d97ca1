{"version": 3, "file": "AppleAuthenticationButton.js", "sourceRoot": "", "sources": ["../src/AppleAuthenticationButton.tsx"], "names": [], "mappings": "AACA,OAAO,6BAA6B,MAAM,iCAAiC,CAAC;AAE5E,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,CAAC,OAAO,UAAU,yBAAyB,CAAC,EAChD,OAAO,EACP,GAAG,SAAS,EACmB;IAC/B,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACnC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,SAAS,CAAC,EAAG,CAAC;AAClF,CAAC", "sourcesContent": ["import { AppleAuthenticationButtonProps } from './AppleAuthentication.types';\nimport ExpoAppleAuthenticationButton from './ExpoAppleAuthenticationButton';\n\n// @needsAudit\n/**\n * This component displays the proprietary \"Sign In with Apple\" / \"Continue with Apple\" button on\n * your screen. The App Store Guidelines require you to use this component to start the\n * authentication process instead of a custom button. Limited customization of the button is\n * available via the provided properties.\n *\n * You should only attempt to render this if [`AppleAuthentication.isAvailableAsync()`](#appleauthenticationisavailableasync)\n * resolves to `true`. This component will render nothing if it is not available, and you will get\n * a warning in development mode (`__DEV__ === true`).\n *\n * The properties of this component extend from `View`; however, you should not attempt to set\n * `backgroundColor` or `borderRadius` with the `style` property. This will not work and is against\n * the App Store Guidelines. Instead, you should use the `buttonStyle` property to choose one of the\n * predefined color styles and the `cornerRadius` property to change the border radius of the\n * button.\n *\n * Make sure to attach height and width via the style props as without these styles, the button will\n * not appear on the screen.\n *\n * @see [Apple\n * Documentation](https://developer.apple.com/documentation/authenticationservices/asauthorizationappleidbutton)\n * for more details.\n */\nexport default function AppleAuthenticationButton({\n  onPress,\n  ...restProps\n}: AppleAuthenticationButtonProps) {\n  if (!ExpoAppleAuthenticationButton) {\n    if (__DEV__) {\n      console.warn(\"'AppleAuthenticationButton' is not available.\");\n    }\n    return null;\n  }\n  return <ExpoAppleAuthenticationButton onButtonPress={onPress} {...restProps} />;\n}\n"]}