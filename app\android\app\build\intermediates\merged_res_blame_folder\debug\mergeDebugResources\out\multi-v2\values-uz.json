{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "84,159,160,161", "startColumns": "4,4,4,4", "startOffsets": "7411,13400,13509,13619", "endColumns": "117,108,109,105", "endOffsets": "7524,13504,13614,13720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5056,5161,5311,5440,5549,5694,5827,5947,6203,6375,6483,6642,6774,6928,7090,7156,7217", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "5156,5306,5435,5544,5689,5822,5942,6048,6370,6478,6637,6769,6923,7085,7151,7212,7292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11420,11485,11551,11621,11685,11764,11832,11934,12028", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "11480,11546,11616,11680,11759,11827,11929,12023,12103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "55,56,57,58,59,60,61,243", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4014,4116,4218,4319,4419,4527,4631,20736", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "4111,4213,4314,4414,4522,4626,4745,20832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1070,1134,1237,1307,1374,1483,1546,1613,1672,1746,1809,1863,1978,2036,2098,2152,2227,2356,2446,2526,2619,2703,2792,2933,3015,3097,3236,3322,3406,3466,3517,3583,3656,3734,3805,3886,3958,4035,4110,4181,4282,4376,4455,4551,4645,4719,4795,4881,4934,5021,5087,5172,5263,5325,5389,5452,5521,5623,5724,5820,5921,5985,6040,6123,6209,6286", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "278,355,435,518,612,699,794,921,1005,1065,1129,1232,1302,1369,1478,1541,1608,1667,1741,1804,1858,1973,2031,2093,2147,2222,2351,2441,2521,2614,2698,2787,2928,3010,3092,3231,3317,3401,3461,3512,3578,3651,3729,3800,3881,3953,4030,4105,4176,4277,4371,4450,4546,4640,4714,4790,4876,4929,5016,5082,5167,5258,5320,5384,5447,5516,5618,5719,5815,5916,5980,6035,6118,6204,6281,6355"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,87,88,158,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,240,241,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,3593,3670,3750,3833,3927,4750,4845,4972,7697,7757,13297,15045,15115,15182,15291,15354,15421,15480,15554,15617,15671,15786,15844,15906,15960,16035,16648,16738,16818,16911,16995,17084,17225,17307,17389,17528,17614,17698,17758,17809,17875,17948,18026,18097,18178,18250,18327,18402,18473,18574,18668,18747,18843,18937,19011,19087,19173,19226,19313,19379,19464,19555,19617,19681,19744,19813,19915,20016,20112,20213,20277,20332,20499,20585,20662", "endLines": "22,50,51,52,53,54,62,63,64,87,88,158,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,240,241,242", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "917,3665,3745,3828,3922,4009,4840,4967,5051,7752,7816,13395,15110,15177,15286,15349,15416,15475,15549,15612,15666,15781,15839,15901,15955,16030,16159,16733,16813,16906,16990,17079,17220,17302,17384,17523,17609,17693,17753,17804,17870,17943,18021,18092,18173,18245,18322,18397,18468,18569,18663,18742,18838,18932,19006,19082,19168,19221,19308,19374,19459,19550,19612,19676,19739,19808,19910,20011,20107,20208,20272,20327,20410,20580,20657,20731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1027,1122,1222,1304,1404,1521,1606,1684,1775,1868,1963,2057,2151,2244,2339,2434,2525,2617,2701,2811,2917,3017,3125,3231,3333,3494,20415", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "1022,1117,1217,1299,1399,1516,1601,1679,1770,1863,1958,2052,2146,2239,2334,2429,2520,2612,2696,2806,2912,3012,3120,3226,3328,3489,3588,20494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,259,379,510,638,785,917,1062,1159,1298,1438", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "164,254,374,505,633,780,912,1057,1154,1293,1433,1574"}, "to": {"startLines": "83,85,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7297,7529,13725,13845,13976,14104,14251,14383,14528,14625,14764,14904", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "7406,7614,13840,13971,14099,14246,14378,14523,14620,14759,14899,15040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,324,423,526,605,705,794,880,969,1036,1139,1224,1316,1393,1481,1555,1653,1721,1787,1867,1951,2042", "endColumns": "77,114,75,98,102,78,99,88,85,88,66,102,84,91,76,87,73,97,67,65,79,83,90,94", "endOffsets": "128,243,319,418,521,600,700,789,875,964,1031,1134,1219,1311,1388,1476,1550,1648,1716,1782,1862,1946,2037,2132"}, "to": {"startLines": "86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7619,7821,7936,8012,8111,8214,8293,8393,8482,8568,8657,8724,8827,8912,9004,9081,9169,9243,16164,16232,16298,16378,16462,16553", "endColumns": "77,114,75,98,102,78,99,88,85,88,66,102,84,91,76,87,73,97,67,65,79,83,90,94", "endOffsets": "7692,7931,8007,8106,8209,8288,8388,8477,8563,8652,8719,8822,8907,8999,9076,9164,9238,9336,16227,16293,16373,16457,16548,16643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "80", "endOffsets": "282"}, "to": {"startLines": "244", "startColumns": "4", "startOffsets": "20837", "endColumns": "84", "endOffsets": "20917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1841,1973,2100,2177,2251,2324,2399,2489,2588,2657,2723,2776,2836,2884,2945,3006,3077,3137,3205,3268,3333,3399,3451,3512,3587,3662,3715", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,51,60,74,74,52,66", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1836,1968,2095,2172,2246,2319,2394,2484,2583,2652,2718,2771,2831,2879,2940,3001,3072,3132,3200,3263,3328,3394,3446,3507,3582,3657,3710,3777"}, "to": {"startLines": "2,11,15,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,563,9341,9427,9522,9605,9703,9802,9877,9945,10046,10147,10212,10275,10338,10410,10538,10670,10797,10874,10948,11021,11096,11186,11285,11354,12108,12161,12221,12269,12330,12391,12462,12522,12590,12653,12718,12914,12966,13027,13102,13177,13230", "endLines": "10,14,18,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,51,60,74,74,52,66", "endOffsets": "375,558,734,9422,9517,9600,9698,9797,9872,9940,10041,10142,10207,10270,10333,10405,10533,10665,10792,10869,10943,11016,11091,11181,11280,11349,11415,12156,12216,12264,12325,12386,12457,12517,12585,12648,12713,12779,12961,13022,13097,13172,13225,13292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6053", "endColumns": "149", "endOffsets": "6198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3399,3463", "endColumns": "63,65", "endOffsets": "3458,3524"}, "to": {"startLines": "150,151", "startColumns": "4,4", "startOffsets": "12784,12848", "endColumns": "63,65", "endOffsets": "12843,12909"}}]}]}