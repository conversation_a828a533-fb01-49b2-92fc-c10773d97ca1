{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\android\\app\\.cxx\\Debug\\5m4y3y2k\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\android\\app\\.cxx\\Debug\\5m4y3y2k\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNGoogleSignInCGen"}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_lottiereactnative"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnsvg", "output": "K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "K:\\2025\\thenextdoor\\app\\android\\app\\build\\intermediates\\cxx\\Debug\\5m4y3y2k\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}