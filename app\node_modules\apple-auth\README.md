#  Sign in with Apple for Node.js

<a href="https://twitter.com/intent/follow?screen_name=ananayarora"><img src="https://img.shields.io/twitter/follow/ananayarora.svg?label=Follow%20@ananayarora" alt="Follow @ananayarora"></img></a>
<a href="https://npmjs.com/package/apple-auth">
  <img src="https://img.shields.io/npm/dt/apple-auth.svg"></img>
  <img src="https://img.shields.io/npm/v/apple-auth.svg"></img>
</a>
</p>

An easy-to-use Node.js library for Signing in with Apple!

Now with support for fetching the name and email!

⚠️ Important note: Apple will only provide you with the name ONCE which is when the user taps "Sign in with <PERSON>" on your app the first time. Keep in mind that you have to store this in your database at this time! For every login after that, Apple will provide you with a unique ID and the email that you can use to lookup the username in your database.

**Check out the passport version of this library here:**

https://github.com/ananay/passport-apple

https://npmjs.com/package/passport-apple

## Setup

Begin by installing the library:
```npm install apple-auth```

The configurations for Sign in with Apple are quite extensive so I've made an extensive SETUP.md file that you can read
https://github.com/ananay/apple-auth/blob/master/SETUP.md

## Example

I've created an example of how to use this library with Express! Check it out here:

https://github.com/ananay/apple-auth-example

**Example live on https://apple.ananay.dev**

## Usage

Initialize it using the following code:
```
const fs = require('fs');
const AppleAuth = require('apple-auth');
const config = fs.readFileSync("./config/config");
const auth = new AppleAuth(config, './config/AuthKey.p8');
```

Methods:
- ```auth.loginURL()``` - Creates the Login URL that your users will use to login to
- ```auth.accessToken(grantCode)``` - Gets the access token from the grant code received
- ```auth.refreshToken(refreshToken)``` - Gets the access token from a refresh token

## Troubleshooting

### `invalid_grant` when authorization code is generated by iOS App
Fix: If the authorizationCode was generated by your app, you should use your App ID as your clientId and not your service one. Discussion: https://github.com/ananay/apple-auth/issues/13

## Questions / Contributing

Feel free to open issues and pull requests. If you would like to be one of the core creators of this library, please reach out to <NAME_EMAIL> or message me on twitter @ananayarora!

<h4> Created with ❤️ by <a href="https://ananayarora.com">Ananay Arora</a></h4>


#### ⚠️ Disclaimer
This repository is NOT developed, endorsed by Apple Inc. or even related at all to Apple Inc. This library was implemented solely by the community's hardwork, and based on information that is public on Apple Developer's website. The library is a helper library for anyone trying to implement Apple's Sign in with Apple.
