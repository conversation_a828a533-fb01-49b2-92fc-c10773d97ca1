-- Merging decision tree log ---
manifest
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:2:1-10:12
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:2:1-10:12
	package
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:3:11-36
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:2:11-69
application
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:5:2-8:16
activity#com.razorpay.CheckoutActivity
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:6:3-7:79
	android:configChanges
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:7:7-77
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml:6:13-57
uses-sdk
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\src\main\AndroidManifest.xml
