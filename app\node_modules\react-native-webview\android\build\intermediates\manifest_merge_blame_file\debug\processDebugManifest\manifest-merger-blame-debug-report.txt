1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.reactnativecommunity.webview" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <queries>
7-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:3:3-13:13
8        <intent>
8-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:4:5-6:14
9            <action android:name="org.chromium.intent.action.PAY" />
9-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:5:7-62
9-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:5:15-60
10        </intent>
11        <intent>
11-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:7:5-9:14
12            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
12-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:8:7-74
12-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:8:15-72
13        </intent>
14        <intent>
14-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:10:5-12:14
15            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
15-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:11:7-81
15-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:11:15-79
16        </intent>
17    </queries>
18
19    <application>
19-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:15:3-25:17
20        <provider
20-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:16:5-24:16
21            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
21-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:17:7-45
22            android:authorities="${applicationId}.fileprovider"
22-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:18:7-58
23            android:exported="false"
23-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:19:7-31
24            android:grantUriPermissions="true" >
24-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:20:7-41
25            <meta-data
25-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:21:7-23:55
26                android:name="android.support.FILE_PROVIDER_PATHS"
26-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:22:9-59
27                android:resource="@xml/file_provider_paths" />
27-->K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:23:9-52
28        </provider>
29    </application>
30
31</manifest>
