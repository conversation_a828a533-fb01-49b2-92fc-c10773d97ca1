import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import { Text } from "./index";
import { BRAND_COLORS } from "../constants/colors";
import { LinearGradient } from "expo-linear-gradient";

/**
 * AchievementBadge Component
 * A beautiful, code-based achievement badge with modern design
 */
const AchievementBadge = ({ achievement, onPress, style }) => {
  const { theme } = useTheme();

  // Enhanced achievement category colors and icons using Miles-inspired palette
  const getAchievementStyle = (category, earned) => {
    const styles = {
      learning: {
        color: BRAND_COLORS.EXPLORER_TEAL,
        icon: earned ? "school" : "school-outline",
        gradient: [BRAND_COLORS.EXPLORER_TEAL, BRAND_COLORS.SKY_AQUA],
        bgColor: earned ? BRAND_COLORS.EXPLORER_TEAL + "20" : BRAND_COLORS.CARD_BACKGROUND,
        borderColor: earned ? BRAND_COLORS.EXPLORER_TEAL + "60" : BRAND_COLORS.EXPLORER_TEAL + "30",
        shadowColor: BRAND_COLORS.EXPLORER_TEAL,
        iconBg: earned ? BRAND_COLORS.EXPLORER_TEAL + "30" : BRAND_COLORS.EXPLORER_TEAL + "15",
        textColor: earned ? BRAND_COLORS.OCEAN_BLUE : BRAND_COLORS.SHADOW_GREY,
      },
      streak: {
        color: BRAND_COLORS.RUCKSACK_BROWN,
        icon: earned ? "flame" : "flame-outline",
        gradient: [BRAND_COLORS.RUCKSACK_BROWN, "#D4A574"],
        bgColor: earned ? BRAND_COLORS.RUCKSACK_BROWN + "20" : BRAND_COLORS.CARD_BACKGROUND,
        borderColor: earned ? BRAND_COLORS.RUCKSACK_BROWN + "60" : BRAND_COLORS.RUCKSACK_BROWN + "30",
        shadowColor: BRAND_COLORS.RUCKSACK_BROWN,
        iconBg: earned ? BRAND_COLORS.RUCKSACK_BROWN + "30" : BRAND_COLORS.RUCKSACK_BROWN + "15",
        textColor: earned ? BRAND_COLORS.OCEAN_BLUE : BRAND_COLORS.SHADOW_GREY,
      },
      xp: {
        color: BRAND_COLORS.OCEAN_BLUE,
        icon: earned ? "star" : "star-outline",
        gradient: [BRAND_COLORS.OCEAN_BLUE, BRAND_COLORS.EXPLORER_TEAL],
        bgColor: earned ? BRAND_COLORS.OCEAN_BLUE + "20" : BRAND_COLORS.CARD_BACKGROUND,
        borderColor: earned ? BRAND_COLORS.OCEAN_BLUE + "60" : BRAND_COLORS.OCEAN_BLUE + "30",
        shadowColor: BRAND_COLORS.OCEAN_BLUE,
        iconBg: earned ? BRAND_COLORS.OCEAN_BLUE + "30" : BRAND_COLORS.OCEAN_BLUE + "15",
        textColor: earned ? BRAND_COLORS.OCEAN_BLUE : BRAND_COLORS.SHADOW_GREY,
      },
      completion: {
        color: BRAND_COLORS.SKY_AQUA,
        icon: earned ? "checkmark-circle" : "checkmark-circle-outline",
        gradient: [BRAND_COLORS.SKY_AQUA, BRAND_COLORS.EXPLORER_TEAL],
        bgColor: earned ? BRAND_COLORS.SKY_AQUA + "20" : BRAND_COLORS.CARD_BACKGROUND,
        borderColor: earned ? BRAND_COLORS.SKY_AQUA + "60" : BRAND_COLORS.SKY_AQUA + "30",
        shadowColor: BRAND_COLORS.SKY_AQUA,
        iconBg: earned ? BRAND_COLORS.SKY_AQUA + "30" : BRAND_COLORS.SKY_AQUA + "15",
        textColor: earned ? BRAND_COLORS.OCEAN_BLUE : BRAND_COLORS.SHADOW_GREY,
      },
      milestone: {
        color: BRAND_COLORS.EXPLORER_TEAL,
        icon: earned ? "trophy" : "trophy-outline",
        gradient: [BRAND_COLORS.EXPLORER_TEAL, BRAND_COLORS.SKY_AQUA],
        bgColor: earned ? BRAND_COLORS.EXPLORER_TEAL + "20" : BRAND_COLORS.CARD_BACKGROUND,
        borderColor: earned ? BRAND_COLORS.EXPLORER_TEAL + "60" : BRAND_COLORS.EXPLORER_TEAL + "30",
        shadowColor: BRAND_COLORS.EXPLORER_TEAL,
        iconBg: earned ? BRAND_COLORS.EXPLORER_TEAL + "30" : BRAND_COLORS.EXPLORER_TEAL + "15",
        textColor: earned ? BRAND_COLORS.OCEAN_BLUE : BRAND_COLORS.SHADOW_GREY,
      },
      default: {
        color: BRAND_COLORS.EXPLORER_TEAL,
        icon: earned ? "trophy" : "trophy-outline",
        gradient: [BRAND_COLORS.EXPLORER_TEAL, BRAND_COLORS.SKY_AQUA],
        bgColor: earned ? BRAND_COLORS.EXPLORER_TEAL + "20" : BRAND_COLORS.CARD_BACKGROUND,
        borderColor: earned ? BRAND_COLORS.EXPLORER_TEAL + "60" : BRAND_COLORS.EXPLORER_TEAL + "30",
        shadowColor: BRAND_COLORS.EXPLORER_TEAL,
        iconBg: earned ? BRAND_COLORS.EXPLORER_TEAL + "30" : BRAND_COLORS.EXPLORER_TEAL + "15",
        textColor: earned ? BRAND_COLORS.OCEAN_BLUE : BRAND_COLORS.SHADOW_GREY,
      },
    };
    return styles[category] || styles.default;
  };

  const achievementStyle = getAchievementStyle(
    achievement.category,
    achievement.earned
  );

  return (
    <View style={[{ position: "relative" }, style]}>
      {/* XP Reward Badge - Positioned at top center */}
      {achievement.earned && achievement.xpReward && (
        <View
          style={{
            position: "absolute",
            top: -12,
            left: "50%",
            transform: [{ translateX: -30 }], // Half of badge width (60px)
            zIndex: 10,
            backgroundColor: BRAND_COLORS.OCEAN_BLUE,
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            flexDirection: "row",
            alignItems: "center",
            shadowColor: theme.colors.oceanBlue,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.15,
            shadowRadius: 8,
            elevation: 0,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Ionicons
            name="star"
            size={12}
            color="#FFF"
            style={{ marginRight: 4 }}
          />
          <Text
            variant="caption"
            weight="bold"
            style={{
              fontSize: 10,
              color: "#FFF",
              fontFamily: theme.typography.fontFamily.bold,
              textAlign: "center",
            }}
          >
            +{achievement.xpReward}
          </Text>
        </View>
      )}

      {/* Progress Badge - Positioned at top center for ongoing achievements */}
      {!achievement.earned && achievement.progress > 0 && (
        <View
          style={{
            position: "absolute",
            top: -12,
            left: "50%",
            transform: [{ translateX: -25 }], // Half of badge width (50px)
            zIndex: 10,
            backgroundColor: theme.colors.brandGreen,
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            shadowColor: theme.colors.brandGreen,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.4,
            shadowRadius: 6,
            aspectRatio: "1/1",
            width: 35,
            alignItems: "center",
            justifyContent: "center",
            elevation: 8,
          }}
        >
          <Text
            variant="caption"
            weight="bold"
            style={{
              fontSize: 10,
              color: "#FFF",
              fontFamily: theme.typography.fontFamily.bold,
              textAlign: "center",
            }}
          >
            {Math.round(achievement.progress)}%
          </Text>
        </View>
      )}

      <TouchableOpacity
        onPress={onPress}
        style={{
          width: 160,
          height: 180,
          borderRadius: 20,
          overflow: 'hidden',
          borderWidth: achievement.earned ? 2 : 1,
          borderColor: achievementStyle.borderColor,
          shadowColor: achievement.earned ? achievementStyle.shadowColor : theme.colors.oceanBlue,
          shadowOffset: { width: 0, height: achievement.earned ? 4 : 2 },
          shadowOpacity: achievement.earned ? 0.2 : 0.05,
          shadowRadius: achievement.earned ? 12 : 8,
          elevation: 0,
        }}
      >
        {achievement.earned ? (
          <LinearGradient
            colors={[achievementStyle.bgColor, BRAND_COLORS.CARD_BACKGROUND]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{
              flex: 1,
              padding: theme.spacing.lg,
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            {/* Content for earned achievements */}
            {renderAchievementContent()}
          </LinearGradient>
        ) : (
          <View
            style={{
              flex: 1,
              backgroundColor: BRAND_COLORS.CARD_BACKGROUND,
              padding: theme.spacing.lg,
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            {/* Content for unearned achievements */}
            {renderAchievementContent()}
          </View>
        )}
      </TouchableOpacity>
    </View>
  );

  // Helper function to render achievement content
  function renderAchievementContent() {
    return (
      <>
        {/* Badge Header with Icon */}
        <View style={{ alignItems: "center", flex: 1, justifyContent: "center" }}>
          {/* Enhanced Achievement Icon */}
          {achievement.earned ? (
            <LinearGradient
              colors={[achievementStyle.color + "40", achievementStyle.color + "20"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{
                width: 72,
                height: 72,
                borderRadius: 36,
                alignItems: "center",
                justifyContent: "center",
                marginBottom: theme.spacing.sm,
                borderWidth: 2,
                borderColor: achievementStyle.color + "80",
                shadowColor: achievementStyle.shadowColor,
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 0,
              }}
            >
              <Ionicons
                name={achievementStyle.icon}
                size={36}
                color={achievementStyle.color}
              />
            </LinearGradient>
          ) : (
            <View
              style={{
                width: 64,
                height: 64,
                borderRadius: 32,
                backgroundColor: achievementStyle.iconBg,
                alignItems: "center",
                justifyContent: "center",
                marginBottom: theme.spacing.sm,
                borderWidth: 1,
                borderColor: achievementStyle.color + "30",
                shadowColor: "transparent",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0,
                shadowRadius: 4,
                elevation: 0,
              }}
            >
              <Ionicons
                name={achievementStyle.icon}
                size={32}
                color={achievementStyle.color}
              />
            </View>
          )}
        </View>

        {/* Badge Footer with Text */}
        <View style={{ alignItems: "center" }}>
          <Text
            variant="caption"
            weight="semibold"
            align="center"
            numberOfLines={2}
            style={{
              marginBottom: 4,
              color: achievement.earned
                ? achievementStyle.textColor
                : theme.colors.neutral[600],
              fontFamily: theme.typography.fontFamily.semibold,
              fontSize: 12,
              lineHeight: 16,
            }}
          >
            {achievement.title}
          </Text>
          <Text
            variant="caption"
            align="center"
            numberOfLines={2}
            style={{
              color: achievement.earned
                ? achievementStyle.color
                : theme.colors.neutral[500],
              fontFamily: theme.typography.fontFamily.regular,
              fontSize: 10,
              lineHeight: 14,
            }}
          >
            {achievement.description}
          </Text>
        </View>

        {/* Earned Badge Overlay */}
        {achievement.earned && (
          <View
            style={{
              position: "absolute",
              top: 12,
              right: 12,
              width: 28,
              height: 28,
              borderRadius: 14,
              backgroundColor: achievementStyle.color,
              alignItems: "center",
              justifyContent: "center",
              shadowColor: achievementStyle.shadowColor,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 0,
              borderWidth: 2,
              borderColor: BRAND_COLORS.CARD_BACKGROUND,
            }}
          >
            <Ionicons name="checkmark" size={16} color="#FFF" />
          </View>
        )}
      </>
    );
  };
};

export default AchievementBadge;
