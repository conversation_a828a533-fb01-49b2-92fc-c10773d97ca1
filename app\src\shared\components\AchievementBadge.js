import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import { Text } from "./index";
import { BRAND_COLORS } from "../constants/colors";

/**
 * AchievementBadge Component
 * A beautiful, code-based achievement badge with modern design
 */
const AchievementBadge = ({ achievement, onPress, style }) => {
  const { theme } = useTheme();

  // Enhanced achievement category colors and icons using Miles-inspired palette
  const getAchievementStyle = (category, earned) => {
    const styles = {
      learning: {
        color: BRAND_COLORS.EXPLORER_TEAL,
        icon: earned ? "school" : "school-outline",
        gradient: [BRAND_COLORS.EXPLORER_TEAL, BRAND_COLORS.SKY_AQUA],
        bgColor: BRAND_COLORS.EXPLORER_TEAL + "15",
        borderColor: BRAND_COLORS.EXPLORER_TEAL + "40",
        shadowColor: BRAND_COLORS.EXPLORER_TEAL,
      },
      streak: {
        color: BRAND_COLORS.RUCKSACK_BROWN,
        icon: earned ? "flame" : "flame-outline",
        gradient: [BRAND_COLORS.RUCKSACK_BROWN, "#D4A574"],
        bgColor: BRAND_COLORS.RUCKSACK_BROWN + "15",
        borderColor: BRAND_COLORS.RUCKSACK_BROWN + "40",
        shadowColor: BRAND_COLORS.RUCKSACK_BROWN,
      },
      xp: {
        color: BRAND_COLORS.OCEAN_BLUE,
        icon: earned ? "star" : "star-outline",
        gradient: [BRAND_COLORS.OCEAN_BLUE, BRAND_COLORS.EXPLORER_TEAL],
        bgColor: BRAND_COLORS.OCEAN_BLUE + "15",
        borderColor: BRAND_COLORS.OCEAN_BLUE + "40",
        shadowColor: BRAND_COLORS.OCEAN_BLUE,
      },
      completion: {
        color: BRAND_COLORS.OCEAN_BLUE,
        icon: earned ? "checkmark-circle" : "checkmark-circle-outline",
        gradient: [BRAND_COLORS.OCEAN_BLUE, BRAND_COLORS.SKY_AQUA],
        bgColor: BRAND_COLORS.OCEAN_BLUE + "15",
        borderColor: BRAND_COLORS.OCEAN_BLUE + "40",
        shadowColor: BRAND_COLORS.OCEAN_BLUE,
      },
      milestone: {
        color: BRAND_COLORS.SKY_AQUA,
        icon: earned ? "trophy" : "trophy-outline",
        gradient: [BRAND_COLORS.SKY_AQUA, BRAND_COLORS.EXPLORER_TEAL],
        bgColor: BRAND_COLORS.SKY_AQUA + "15",
        borderColor: BRAND_COLORS.SKY_AQUA + "40",
        shadowColor: BRAND_COLORS.SKY_AQUA,
      },
      default: {
        color: BRAND_COLORS.EXPLORER_TEAL,
        icon: earned ? "trophy" : "trophy-outline",
        gradient: [BRAND_COLORS.EXPLORER_TEAL, BRAND_COLORS.SKY_AQUA],
        bgColor: BRAND_COLORS.EXPLORER_TEAL + "15",
        borderColor: BRAND_COLORS.EXPLORER_TEAL + "40",
        shadowColor: BRAND_COLORS.EXPLORER_TEAL,
      },
    };
    return styles[category] || styles.default;
  };

  const achievementStyle = getAchievementStyle(
    achievement.category,
    achievement.earned
  );

  return (
    <View style={[{ position: "relative" }, style]}>
      {/* XP Reward Badge - Positioned at top center */}
      {achievement.earned && achievement.xpReward && (
        <View
          style={{
            position: "absolute",
            top: -12,
            left: "50%",
            transform: [{ translateX: -30 }], // Half of badge width (60px)
            zIndex: 10,
            backgroundColor: BRAND_COLORS.OCEAN_BLUE,
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            flexDirection: "row",
            alignItems: "center",
            shadowColor: theme.colors.oceanBlue,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.15,
            shadowRadius: 8,
            elevation: 0,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Ionicons
            name="star"
            size={12}
            color="#FFF"
            style={{ marginRight: 4 }}
          />
          <Text
            variant="caption"
            weight="bold"
            style={{
              fontSize: 10,
              color: "#FFF",
              fontFamily: theme.typography.fontFamily.bold,
              textAlign: "center",
            }}
          >
            +{achievement.xpReward}
          </Text>
        </View>
      )}

      {/* Progress Badge - Positioned at top center for ongoing achievements */}
      {!achievement.earned && achievement.progress > 0 && (
        <View
          style={{
            position: "absolute",
            top: -12,
            left: "50%",
            transform: [{ translateX: -25 }], // Half of badge width (50px)
            zIndex: 10,
            backgroundColor: theme.colors.brandGreen,
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            shadowColor: theme.colors.brandGreen,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.4,
            shadowRadius: 6,
            aspectRatio: "1/1",
            width: 35,
            alignItems: "center",
            justifyContent: "center",
            elevation: 8,
          }}
        >
          <Text
            variant="caption"
            weight="bold"
            style={{
              fontSize: 10,
              color: "#FFF",
              fontFamily: theme.typography.fontFamily.bold,
              textAlign: "center",
            }}
          >
            {Math.round(achievement.progress)}%
          </Text>
        </View>
      )}

      <TouchableOpacity
        onPress={onPress}
        style={{
          width: 160,
          height: 180,
          backgroundColor: achievement.earned
            ? achievementStyle.bgColor
            : BRAND_COLORS.CARD_BACKGROUND,
          borderRadius: 20,
          padding: theme.spacing.lg,
          alignItems: "center",
          justifyContent: "space-between",
          borderWidth: achievement.earned ? 2 : 1,
          borderColor: achievement.earned
            ? achievementStyle.borderColor
            : theme.colors.explorerTeal + "20",
          shadowColor: achievement.earned
            ? achievementStyle.shadowColor
            : theme.colors.oceanBlue,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: achievement.earned ? 0.15 : 0.05,
          shadowRadius: 8,
          elevation: 0,
        }}
      >
        {/* Badge Header with Icon */}
        <View
          style={{ alignItems: "center", flex: 1, justifyContent: "center" }}
        >
          {/* Main Achievement Icon */}
          <View
            style={{
              width: 64,
              height: 64,
              borderRadius: 32,
              backgroundColor: achievement.earned
                ? achievementStyle.color + "25"
                : theme.colors.neutral[100],
              alignItems: "center",
              justifyContent: "center",
              marginBottom: theme.spacing.sm,
              borderWidth: achievement.earned ? 2 : 1,
              borderColor: achievement.earned
                ? achievementStyle.color + "60"
                : theme.colors.neutral[200],
              shadowColor: achievement.earned
                ? achievementStyle.shadowColor
                : "transparent",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: achievement.earned ? 0.2 : 0,
              shadowRadius: 4,
              elevation: 0,
            }}
          >
            <Ionicons
              name={achievementStyle.icon}
              size={32}
              color={
                achievement.earned
                  ? achievementStyle.color
                  : theme.colors.neutral[400]
              }
            />
          </View>
        </View>

        {/* Badge Footer with Text */}
        <View style={{ alignItems: "center" }}>
          <Text
            variant="caption"
            weight="semibold"
            align="center"
            numberOfLines={2}
            style={{
              marginBottom: 4,
              color: achievement.earned
                ? theme.colors.brandNavy
                : theme.colors.neutral[600],
              fontFamily: theme.typography.fontFamily.semibold,
              fontSize: 12,
              lineHeight: 16,
            }}
          >
            {achievement.title}
          </Text>
          <Text
            variant="caption"
            align="center"
            numberOfLines={2}
            style={{
              color: achievement.earned
                ? achievementStyle.color
                : theme.colors.neutral[500],
              fontFamily: theme.typography.fontFamily.regular,
              fontSize: 10,
              lineHeight: 14,
            }}
          >
            {achievement.description}
          </Text>
        </View>

        {/* Earned Badge Overlay */}
        {achievement.earned && (
          <View
            style={{
              position: "absolute",
              top: 12,
              right: 12,
              width: 24,
              height: 24,
              borderRadius: 12,
              backgroundColor: achievementStyle.color,
              alignItems: "center",
              justifyContent: "center",
              shadowColor: theme.colors.oceanBlue,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.15,
              shadowRadius: 8,
              elevation: 0,
            }}
          >
            <Ionicons name="checkmark" size={14} color="#FFF" />
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default AchievementBadge;
