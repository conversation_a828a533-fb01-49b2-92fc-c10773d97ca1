{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,232,306,407,502,593,679,763,861,937,999,1092,1170,1264,1341,1435,1509,1594,1663,1733,1807,1884,1969", "endColumns": "78,97,73,100,94,90,85,83,97,75,61,92,77,93,76,93,73,84,68,69,73,76,84,89", "endOffsets": "129,227,301,402,497,588,674,758,856,932,994,1087,1165,1259,1336,1430,1504,1589,1658,1728,1802,1879,1964,2054"}, "to": {"startLines": "88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,193,194,195,196,197,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7679,7878,7976,8050,8151,8246,8337,8423,8507,8605,8681,8743,8836,8914,9008,9085,9179,9253,16155,16224,16294,16368,16445,16530", "endColumns": "78,97,73,100,94,90,85,83,97,75,61,92,77,93,76,93,73,84,68,69,73,76,84,89", "endOffsets": "7753,7971,8045,8146,8241,8332,8418,8502,8600,8676,8738,8831,8909,9003,9080,9174,9248,9333,16219,16289,16363,16440,16525,16615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,249,371,500,629,763,895,1029,1125,1269,1414", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "157,244,366,495,624,758,890,1024,1120,1264,1409,1535"}, "to": {"startLines": "85,87,164,165,166,167,168,169,170,171,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7384,7592,13520,13642,13771,13900,14034,14166,14300,14396,14540,14685", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "7486,7674,13637,13766,13895,14029,14161,14295,14391,14535,14680,14806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3260,3325", "endColumns": "64,67", "endOffsets": "3320,3388"}, "to": {"startLines": "152,153", "startColumns": "4,4", "startOffsets": "12603,12668", "endColumns": "64,67", "endOffsets": "12663,12731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3313,3373,3447,3521,3574", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,52,59,73,73,52,64", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3308,3368,3442,3516,3569,3634"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,582,9338,9420,9500,9583,9670,9764,9832,9896,9986,10077,10142,10210,10270,10338,10451,10570,10681,10753,10832,10903,10973,11055,11135,11199,11938,11991,12049,12097,12158,12219,12286,12348,12414,12473,12538,12736,12789,12849,12923,12997,13050", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,52,59,73,73,52,64", "endOffsets": "376,577,761,9415,9495,9578,9665,9759,9827,9891,9981,10072,10137,10205,10265,10333,10446,10565,10676,10748,10827,10898,10968,11050,11130,11194,11257,11986,12044,12092,12153,12214,12281,12343,12409,12468,12533,12598,12784,12844,12918,12992,13045,13110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,272,343,425,492,559,633,709,789,869,937,1020,1102,1177,1263,1350,1425,1496,1567,1658,1730,1805,1874", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,198,267,338,420,487,554,628,704,784,864,932,1015,1097,1172,1258,1345,1420,1491,1562,1653,1725,1800,1869,1942"}, "to": {"startLines": "50,66,174,176,177,179,199,200,201,248,249,250,251,256,257,258,259,260,261,262,263,265,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3615,5146,14811,14945,15016,15157,16620,16687,16761,20586,20666,20746,20814,21212,21294,21369,21455,21542,21617,21688,21759,21951,22023,22098,22167", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "3679,5220,14875,15011,15093,15219,16682,16756,16832,20661,20741,20809,20892,21289,21364,21450,21537,21612,21683,21754,21845,22018,22093,22162,22235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "86,161,162,163", "startColumns": "4,4,4,4", "startOffsets": "7491,13206,13307,13418", "endColumns": "100,100,110,101", "endOffsets": "7587,13302,13413,13515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6206", "endColumns": "142", "endOffsets": "6344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11262,11332,11397,11466,11535,11610,11674,11771,11865", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "11327,11392,11461,11530,11605,11669,11766,11860,11933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5225,5332,5500,5623,5735,5880,6001,6109,6349,6499,6607,6761,6885,7024,7177,7237,7303", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "5327,5495,5618,5730,5875,5996,6104,6201,6494,6602,6756,6880,7019,7172,7232,7298,7379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "74", "endOffsets": "276"}, "to": {"startLines": "269", "startColumns": "4", "startOffsets": "22240", "endColumns": "78", "endOffsets": "22314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1046,1109,1200,1265,1324,1412,1474,1536,1596,1663,1726,1780,1894,1951,2012,2066,2136,2255,2336,2413,2502,2584,2669,2804,2881,2958,3099,3185,3269,3325,3377,3443,3513,3591,3662,3744,3814,3890,3961,4030,4144,4240,4314,4412,4508,4582,4652,4754,4809,4897,4964,5051,5144,5207,5271,5334,5400,5500,5609,5703,5810,5870,5926,6004,6088,6166", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "254,339,426,509,602,686,786,902,984,1041,1104,1195,1260,1319,1407,1469,1531,1591,1658,1721,1775,1889,1946,2007,2061,2131,2250,2331,2408,2497,2579,2664,2799,2876,2953,3094,3180,3264,3320,3372,3438,3508,3586,3657,3739,3809,3885,3956,4025,4139,4235,4309,4407,4503,4577,4647,4749,4804,4892,4959,5046,5139,5202,5266,5329,5395,5495,5604,5698,5805,5865,5921,5999,6083,6161,6234"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,89,90,160,175,178,180,181,182,183,184,185,186,187,188,189,190,191,192,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,253,254,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3684,3769,3856,3939,4032,4848,4948,5064,7758,7815,13115,14880,15098,15224,15312,15374,15436,15496,15563,15626,15680,15794,15851,15912,15966,16036,16837,16918,16995,17084,17166,17251,17386,17463,17540,17681,17767,17851,17907,17959,18025,18095,18173,18244,18326,18396,18472,18543,18612,18726,18822,18896,18994,19090,19164,19234,19336,19391,19479,19546,19633,19726,19789,19853,19916,19982,20082,20191,20285,20392,20452,20508,20977,21061,21139", "endLines": "22,51,52,53,54,55,63,64,65,89,90,160,175,178,180,181,182,183,184,185,186,187,188,189,190,191,192,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,253,254,255", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "920,3764,3851,3934,4027,4111,4943,5059,5141,7810,7873,13201,14940,15152,15307,15369,15431,15491,15558,15621,15675,15789,15846,15907,15961,16031,16150,16913,16990,17079,17161,17246,17381,17458,17535,17676,17762,17846,17902,17954,18020,18090,18168,18239,18321,18391,18467,18538,18607,18721,18817,18891,18989,19085,19159,19229,19331,19386,19474,19541,19628,19721,19784,19848,19911,19977,20077,20186,20280,20387,20447,20503,20581,21056,21134,21207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1142,1249,1339,1440,1552,1630,1707,1798,1891,1984,2081,2181,2274,2369,2463,2554,2645,2725,2832,2933,3030,3139,3241,3355,3512,20897", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "1031,1137,1244,1334,1435,1547,1625,1702,1793,1886,1979,2076,2176,2269,2364,2458,2549,2640,2720,2827,2928,3025,3134,3236,3350,3507,3610,20972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,264", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4116,4216,4320,4421,4524,4626,4731,21850", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "4211,4315,4416,4519,4621,4726,4843,21946"}}]}]}