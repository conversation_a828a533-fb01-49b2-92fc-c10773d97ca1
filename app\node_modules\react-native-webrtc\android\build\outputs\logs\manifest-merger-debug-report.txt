-- Merging decision tree log ---
manifest
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:1:1-11:12
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:1:1-11:12
	package
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:2:11-42
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:3:11-57
	xmlns:android
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:1:11-69
application
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:5:5-10:19
service#com.oney.WebRTCModule.MediaProjectionService
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:6:9-9:19
	android:foregroundServiceType
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:8:17-64
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml:7:17-55
uses-sdk
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\src\main\AndroidManifest.xml
