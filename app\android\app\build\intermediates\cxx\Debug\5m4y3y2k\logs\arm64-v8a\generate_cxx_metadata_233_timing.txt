# C/C++ build system timings
generate_cxx_metadata
  [gap of 210ms]
  create-invalidation-state 129ms
  generate-prefab-packages
    [gap of 186ms]
    exec-prefab 1490ms
    [gap of 121ms]
  generate-prefab-packages completed in 1797ms
  execute-generate-process
    exec-configure 1742ms
    [gap of 101ms]
  execute-generate-process completed in 1845ms
  [gap of 123ms]
  write-metadata-json-to-file 34ms
generate_cxx_metadata completed in 4143ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 46ms]
  create-invalidation-state 54ms
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 130ms

