{"version": 3, "file": "ExpoAppleAuthentication.js", "sourceRoot": "", "sources": ["../src/ExpoAppleAuthentication.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,2BAA2B,EAAE,MAAM,mBAAmB,CAAC;AAEhE,0EAA0E;AAC1E,uEAAuE;AACvE,sDAAsD;AACtD,eAAe,2BAA2B,CAAC,yBAAyB,CAAC,IAAI;IACvE,gBAAgB;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,kEAAkE;IAClE,8DAA8D;IAC9D,WAAW;QACT,uCAAuC;IACzC,CAAC;IAED,kEAAkE;IAClE,8DAA8D;IAC9D,eAAe;QACb,uCAAuC;QACvC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF,CAAC", "sourcesContent": ["import { requireOptionalNativeModule } from 'expo-modules-core';\n\n// If the real native module doesn't exist, make a pretend one, instead of\n// `null`, so we can offer an `isAvailableAsync` (that will always give\n// `false`, because we're on an unsupported platform).\nexport default requireOptionalNativeModule('ExpoAppleAuthentication') || {\n  isAvailableAsync(): Promise<boolean> {\n    return Promise.resolve(false);\n  },\n\n  // RN v0.65 gives a console warning if this method is missing; see\n  //   https://github.com/facebook/react-native/commit/114be1d21\n  addListener() {\n    // Nothing to do; unsupported platform.\n  },\n\n  // RN v0.65 gives a console warning if this method is missing; see\n  //   https://github.com/facebook/react-native/commit/114be1d21\n  removeListeners() {\n    // Nothing to do; unsupported platform.\n    return Promise.resolve();\n  },\n};\n"]}