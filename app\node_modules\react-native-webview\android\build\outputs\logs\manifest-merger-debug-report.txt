-- Merging decision tree log ---
manifest
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:1:1-26:12
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:1:1-26:12
	package
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml
	xmlns:android
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:1:11-69
queries
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:3:3-13:13
intent#action:name:org.chromium.intent.action.PAY
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:4:5-6:14
action#org.chromium.intent.action.PAY
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:5:7-62
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:5:15-60
intent#action:name:org.chromium.intent.action.IS_READY_TO_PAY
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:7:5-9:14
action#org.chromium.intent.action.IS_READY_TO_PAY
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:8:7-74
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:8:15-72
intent#action:name:org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:10:5-12:14
action#org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:11:7-81
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:11:15-79
application
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:15:3-25:17
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:16:5-24:16
	android:grantUriPermissions
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:20:7-41
	android:authorities
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:18:7-58
	android:exported
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:19:7-31
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:17:7-45
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:21:7-23:55
	android:resource
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:23:9-52
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml:22:9-59
uses-sdk
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml reason: use-sdk injection requested
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml
INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml
	android:targetSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml
	android:minSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\react-native-webview\android\src\main\AndroidManifestNew.xml
