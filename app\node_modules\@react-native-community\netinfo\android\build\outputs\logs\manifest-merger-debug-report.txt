-- Merging decision tree log ---
manifest
ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:2:1-11:12
INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:2:1-11:12
	package
		ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:4:2-44
		INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:3:2-60
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:6:2-7:60
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:7:3-57
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:8:2-9:57
	android:name
		ADDED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:9:3-54
uses-sdk
INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml
INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml
