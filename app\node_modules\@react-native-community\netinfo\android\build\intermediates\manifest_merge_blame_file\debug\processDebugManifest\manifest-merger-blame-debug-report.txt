1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.reactnativecommunity.netinfo" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
7-->K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:6:2-7:60
7-->K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:7:3-57
8    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
8-->K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:8:2-9:57
8-->K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\src\main\AndroidManifest.xml:9:3-54
9
10</manifest>
