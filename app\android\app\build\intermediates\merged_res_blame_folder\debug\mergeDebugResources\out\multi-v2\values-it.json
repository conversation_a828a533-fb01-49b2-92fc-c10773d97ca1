{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6278", "endColumns": "135", "endOffsets": "6409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "86,162,163,164", "startColumns": "4,4,4,4", "startOffsets": "7612,13691,13789,13899", "endColumns": "99,97,109,102", "endOffsets": "7707,13784,13894,13997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "50,66,175,182,183,204,205,255,256,257,262,263,264,265,266,267,268,269,271,272,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3620,5195,15355,15837,15909,17546,17626,21708,21798,21879,22295,22381,22458,22538,22617,22692,22762,22831,23022,23097,23178", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3685,5274,15421,15904,15992,17621,17705,21793,21874,21962,22376,22453,22533,22612,22687,22757,22826,22916,23092,23173,23260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-it\\values-it.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3437,3502", "endColumns": "64,65", "endOffsets": "3497,3563"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "13071,13136", "endColumns": "64,65", "endOffsets": "13131,13197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,230,309,412,508,598,677,774,864,970,1038,1151,1251,1340,1415,1514,1601,1695,1772,1843,1922,2002,2100", "endColumns": "81,92,78,102,95,89,78,96,89,105,67,112,99,88,74,98,86,93,76,70,78,79,97,101", "endOffsets": "132,225,304,407,503,593,672,769,859,965,1033,1146,1246,1335,1410,1509,1596,1690,1767,1838,1917,1997,2095,2197"}, "to": {"startLines": "89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,198,199,200,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7865,8077,8170,8249,8352,8448,8538,8617,8714,8804,8910,8978,9091,9191,9280,9355,9454,9541,17039,17116,17187,17266,17346,17444", "endColumns": "81,92,78,102,95,89,78,96,89,105,67,112,99,88,74,98,86,93,76,70,78,79,97,101", "endOffsets": "7942,8165,8244,8347,8443,8533,8612,8709,8799,8905,8973,9086,9186,9275,9350,9449,9536,9630,17111,17182,17261,17341,17439,17541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,374,509,655,811,941,1099,1201,1338,1489", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "161,256,369,504,650,806,936,1094,1196,1333,1484,1609"}, "to": {"startLines": "85,88,165,166,167,168,169,170,171,172,173,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7501,7770,14002,14115,14250,14396,14552,14682,14840,14942,15079,15230", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "7607,7860,14110,14245,14391,14547,14677,14835,14937,15074,15225,15350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\35a00de50ceac9bdbb8fc43812824536\\transformed\\android-image-cropper-4.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,162,240,314,386,439,496,567", "endColumns": "57,48,77,73,71,52,56,70,55", "endOffsets": "108,157,235,309,381,434,491,562,618"}, "to": {"startLines": "87,176,177,178,179,180,252,253,254", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7712,15426,15475,15553,15627,15699,21524,21581,21652", "endColumns": "57,48,77,73,71,52,56,70,55", "endOffsets": "7765,15470,15548,15622,15694,15747,21576,21647,21703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11715,11786,11847,11919,11989,12065,12131,12218,12303", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "11781,11842,11914,11984,12060,12126,12213,12298,12372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1838,1972,2098,2168,2261,2336,2412,2508,2606,2675,2743,2796,2854,2902,2963,3037,3108,3171,3252,3310,3371,3437,3489,3551,3627,3703,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1833,1967,2093,2163,2256,2331,2407,2503,2601,2670,2738,2791,2849,2897,2958,3032,3103,3166,3247,3305,3366,3432,3484,3546,3622,3698,3756,3826"}, "to": {"startLines": "2,11,15,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,570,9635,9721,9809,9888,9980,10072,10150,10215,10315,10413,10478,10546,10611,10682,10810,10944,11070,11140,11233,11308,11384,11480,11578,11647,12377,12430,12488,12536,12597,12671,12742,12805,12886,12944,13005,13202,13254,13316,13392,13468,13526", "endLines": "10,14,18,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "376,565,753,9716,9804,9883,9975,10067,10145,10210,10310,10408,10473,10541,10606,10677,10805,10939,11065,11135,11228,11303,11379,11475,11573,11642,11710,12425,12483,12531,12592,12666,12737,12800,12881,12939,13000,13066,13249,13311,13387,13463,13521,13591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1031,1134,1243,1327,1432,1551,1629,1704,1796,1890,1983,2077,2178,2272,2369,2464,2556,2648,2729,2835,2942,3040,3144,3250,3357,3520,21967", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "1026,1129,1238,1322,1427,1546,1624,1699,1791,1885,1978,2072,2173,2267,2364,2459,2551,2643,2724,2830,2937,3035,3139,3245,3352,3515,3615,22044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,90,91,161,181,184,185,186,187,188,189,190,191,192,193,194,195,196,197,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "758,3690,3774,3855,3932,4031,4873,4972,5112,7947,8011,13596,15752,15997,16059,16147,16209,16278,16341,16414,16477,16531,16652,16709,16771,16825,16902,17710,17795,17875,17974,18060,18142,18277,18358,18439,18585,18676,18766,18821,18872,18938,19011,19091,19162,19242,19317,19394,19463,19540,19645,19733,19822,19915,20008,20082,20162,20256,20307,20391,20457,20541,20629,20691,20755,20818,20886,21001,21115,21221,21330,21389,21444,22049,22134,22213", "endLines": "22,51,52,53,54,55,63,64,65,90,91,161,181,184,185,186,187,188,189,190,191,192,193,194,195,196,197,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,259,260,261", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "921,3769,3850,3927,4026,4121,4967,5107,5190,8006,8072,13686,15832,16054,16142,16204,16273,16336,16409,16472,16526,16647,16704,16766,16820,16897,17034,17790,17870,17969,18055,18137,18272,18353,18434,18580,18671,18761,18816,18867,18933,19006,19086,19157,19237,19312,19389,19458,19535,19640,19728,19817,19910,20003,20077,20157,20251,20302,20386,20452,20536,20624,20686,20750,20813,20881,20996,21110,21216,21325,21384,21439,21519,22129,22208,22290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5279,5384,5535,5660,5768,5926,6054,6174,6414,6571,6678,6832,6959,7115,7296,7363,7424", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "5379,5530,5655,5763,5921,6049,6169,6273,6566,6673,6827,6954,7110,7291,7358,7419,7496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "74", "endOffsets": "276"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "23265", "endColumns": "78", "endOffsets": "23339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4126,4224,4326,4425,4527,4636,4743,22921", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "4219,4321,4420,4522,4631,4738,4868,23017"}}]}]}