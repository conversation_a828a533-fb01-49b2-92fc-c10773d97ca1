{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-ea8ca34d3b82701c80a4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNGoogleSignInCGen_autolinked_build", "jsonFile": "directory-RNGoogleSignInCGen_autolinked_build-Debug-af0af8fb0688825e2911.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "lottiereactnative_autolinked_build", "jsonFile": "directory-lottiereactnative_autolinked_build-Debug-1bff08e7c8231d327f71.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/lottie-react-native/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-e2eeb5784d567c70c639.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-15fb664504ba0a5b1d67.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-577fc71fcfeea26a54bf.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [9]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-78ec1c926ebe528c171f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-38e5d81c5cf6aa619bf4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [8]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-c53e2cd1067cf89150c9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "K:/2025/thenextdoor/app/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-6af6a0b029d0bb3f3572.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-4aa80318c1147d0f4b4f.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0", "jsonFile": "target-react_codegen_RNGoogleSignInCGen-Debug-611a4481d8128abb4595.json", "name": "react_codegen_RNGoogleSignInCGen", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "jsonFile": "target-react_codegen_lottiereactnative-Debug-df9c4df0ab71ef46320f.json", "name": "react_codegen_lottiereactnative", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-0c8efcbcfcf7526428db.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-4da2192f5109d52a7e0b.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-21185f4c0e5f1c96d9f7.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-47ae079f469f6085a28c.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-f86cf9513a2ea59d1e04.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-9a48abd591732ee9a794.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "K:/2025/thenextdoor/app/android/app/.cxx/Debug/5m4y3y2k/armeabi-v7a", "source": "K:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}