{"version": 3, "file": "AppleAuthentication.js", "sourceRoot": "", "sources": ["../src/AppleAuthentication.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAqB,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAEvF,OAAO,EAGL,4BAA4B,GAM7B,MAAM,6BAA6B,CAAC;AACrC,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAEhE,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,IAAI,CAAC,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,CAAC;QAC1E,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,uBAAuB,CAAC,gBAAgB,EAAE,CAAC;AACpD,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAC/B,OAA0C;IAE1C,IAAI,CAAC,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;QACtE,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,aAAa,CAAC,CAAC;IAC5E,CAAC;IACD,MAAM,cAAc,GAAG;QACrB,GAAG,OAAO;QACV,kBAAkB,EAAE,4BAA4B,CAAC,KAAK;KACvD,CAAC;IACF,MAAM,UAAU,GAAG,MAAM,uBAAuB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IAC9E,IAAI,CAAC,UAAU,CAAC,iBAAiB,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACnF,MAAM,IAAI,UAAU,CAClB,oBAAoB,EACpB,kFAAkF,CACnF,CAAC;IACJ,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY,CAChC,OAA0C;IAE1C,IAAI,CAAC,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;QACtE,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;IAC7E,CAAC;IACD,MAAM,cAAc,GAAG;QACrB,GAAG,OAAO;QACV,kBAAkB,EAAE,4BAA4B,CAAC,OAAO;KACzD,CAAC;IACF,MAAM,UAAU,GAAG,MAAM,uBAAuB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IAC9E,IAAI,CAAC,UAAU,CAAC,iBAAiB,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACnF,MAAM,IAAI,UAAU,CAClB,oBAAoB,EACpB,mFAAmF,CACpF,CAAC;IACJ,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY,CAChC,OAA0C;IAE1C,IAAI,CAAC,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;QACtE,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;IAC7E,CAAC;IACD,MAAM,cAAc,GAAG;QACrB,GAAG,OAAO;QACV,kBAAkB,EAAE,4BAA4B,CAAC,MAAM;KACxD,CAAC;IACF,OAAO,uBAAuB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;AAC9D,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,IAAY;IAEZ,IAAI,CAAC,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,CAAC;QACjF,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,yBAAyB,CAAC,CAAC;IACxF,CAAC;IACD,OAAO,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAC/D,CAAC;AAED,2BAA2B;AAC3B;;;;;;GAMG;AACH,MAAM,UAAU,cAAc,CAC5B,QAAqC,EACrC,WAAoD;IAEpD,IAAI,CAAC,uBAAuB,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;QACxE,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,CAAC;IAC/E,CAAC;IACD,OAAO,uBAAuB,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AACvE,CAAC;AAED,eAAe;AACf,MAAM,UAAU,iBAAiB,CAAC,QAAoB;IACpD,OAAO,uBAAuB,CAAC,WAAW,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC;AACxF,CAAC", "sourcesContent": ["import { CodedError, EventSubscription, UnavailabilityError } from 'expo-modules-core';\n\nimport {\n  AppleAuthenticationCredential,\n  AppleAuthenticationCredentialState,\n  AppleAuthenticationOperation,\n  AppleAuthenticationRefreshOptions,\n  AppleAuthenticationSignInOptions,\n  AppleAuthenticationSignOutOptions,\n  AppleAuthenticationFullName,\n  AppleAuthenticationFullNameFormatStyle,\n} from './AppleAuthentication.types';\nimport ExpoAppleAuthentication from './ExpoAppleAuthentication';\n\n// @needsAudit\n/**\n * Determine if the current device's operating system supports Apple authentication.\n * @return A promise that fulfills with `true` if the system supports Apple authentication, and `false` otherwise.\n */\nexport async function isAvailableAsync(): Promise<boolean> {\n  if (!ExpoAppleAuthentication || !ExpoAppleAuthentication.isAvailableAsync) {\n    return false;\n  }\n  return ExpoAppleAuthentication.isAvailableAsync();\n}\n\n// @needsAudit\n/**\n * Sends a request to the operating system to initiate the Apple authentication flow, which will\n * present a modal to the user over your app and allow them to sign in.\n *\n * You can request access to the user's full name and email address in this method, which allows you\n * to personalize your UI for signed in users. However, users can deny access to either or both\n * of these options at runtime.\n *\n * Additionally, you will only receive Apple Authentication Credentials the first time users sign\n * into your app, so you must store it for later use. It's best to store this information either\n * server-side, or using [SecureStore](./securestore), so that the data persists across app installs.\n * You can use [`AppleAuthenticationCredential.user`](#appleauthenticationcredential) to identify\n * the user, since this remains the same for apps released by the same developer.\n *\n * @param options An optional [`AppleAuthenticationSignInOptions`](#appleauthenticationsigninoptions) object\n * @return A promise that fulfills with an [`AppleAuthenticationCredential`](#appleauthenticationcredential)\n * object after a successful authentication, and rejects with `ERR_REQUEST_CANCELED` if the user cancels the\n * sign-in operation.\n */\nexport async function signInAsync(\n  options?: AppleAuthenticationSignInOptions\n): Promise<AppleAuthenticationCredential> {\n  if (!ExpoAppleAuthentication || !ExpoAppleAuthentication.requestAsync) {\n    throw new UnavailabilityError('expo-apple-authentication', 'signInAsync');\n  }\n  const requestOptions = {\n    ...options,\n    requestedOperation: AppleAuthenticationOperation.LOGIN,\n  };\n  const credential = await ExpoAppleAuthentication.requestAsync(requestOptions);\n  if (!credential.authorizationCode || !credential.identityToken || !credential.user) {\n    throw new CodedError(\n      'ERR_REQUEST_FAILED',\n      'The credential returned by `signInAsync` is missing one or more required fields.'\n    );\n  }\n  return credential;\n}\n\n// @needsAudit\n/**\n * An operation that refreshes the logged-in user’s credentials.\n * Calling this method will show the sign in modal before actually refreshing the user credentials.\n *\n * @param options An [`AppleAuthenticationRefreshOptions`](#appleauthenticationrefreshoptions) object\n * @returns A promise that fulfills with an [`AppleAuthenticationCredential`](#appleauthenticationcredential)\n * object after a successful authentication, and rejects with `ERR_REQUEST_CANCELED` if the user cancels the\n * refresh operation.\n */\nexport async function refreshAsync(\n  options: AppleAuthenticationRefreshOptions\n): Promise<AppleAuthenticationCredential> {\n  if (!ExpoAppleAuthentication || !ExpoAppleAuthentication.requestAsync) {\n    throw new UnavailabilityError('expo-apple-authentication', 'refreshAsync');\n  }\n  const requestOptions = {\n    ...options,\n    requestedOperation: AppleAuthenticationOperation.REFRESH,\n  };\n  const credential = await ExpoAppleAuthentication.requestAsync(requestOptions);\n  if (!credential.authorizationCode || !credential.identityToken || !credential.user) {\n    throw new CodedError(\n      'ERR_REQUEST_FAILED',\n      'The credential returned by `refreshAsync` is missing one or more required fields.'\n    );\n  }\n  return credential;\n}\n\n// @needsAudit\n/**\n * An operation that ends the authenticated session.\n * Calling this method will show the sign in modal before actually signing the user out.\n *\n * It is not recommended to use this method to sign out the user as it works counterintuitively.\n * Instead of using this method it is recommended to simply clear all the user's data collected\n * from using [`signInAsync`](#appleauthenticationsigninasyncoptions) or [`refreshAsync`](#appleauthenticationrefreshasyncoptions) methods.\n *\n * @param options An [`AppleAuthenticationSignOutOptions`](#appleauthenticationsignoutoptions) object\n * @returns A promise that fulfills with an [`AppleAuthenticationCredential`](#appleauthenticationcredential)\n * object after a successful authentication, and rejects with `ERR_REQUEST_CANCELED` if the user cancels the\n * sign-out operation.\n */\nexport async function signOutAsync(\n  options: AppleAuthenticationSignOutOptions\n): Promise<AppleAuthenticationCredential> {\n  if (!ExpoAppleAuthentication || !ExpoAppleAuthentication.requestAsync) {\n    throw new UnavailabilityError('expo-apple-authentication', 'signOutAsync');\n  }\n  const requestOptions = {\n    ...options,\n    requestedOperation: AppleAuthenticationOperation.LOGOUT,\n  };\n  return ExpoAppleAuthentication.requestAsync(requestOptions);\n}\n\n// @needsAudit\n/**\n * Queries the current state of a user credential, to determine if it is still valid or if it has been revoked.\n * > **Note:** This method must be tested on a real device. On the iOS simulator it always throws an error.\n *\n * @param user The unique identifier for the user whose credential state you'd like to check.\n * This should come from the user field of an [`AppleAuthenticationCredential`](#appleauthenticationcredentialstate) object.\n * @return A promise that fulfills with an [`AppleAuthenticationCredentialState`](#appleauthenticationcredentialstate)\n * value depending on the state of the credential.\n */\nexport async function getCredentialStateAsync(\n  user: string\n): Promise<AppleAuthenticationCredentialState> {\n  if (!ExpoAppleAuthentication || !ExpoAppleAuthentication.getCredentialStateAsync) {\n    throw new UnavailabilityError('expo-apple-authentication', 'getCredentialStateAsync');\n  }\n  return ExpoAppleAuthentication.getCredentialStateAsync(user);\n}\n\n// @needsAudit @docsMissing\n/**\n * Creates a locale-aware string representation of a person's name from an object representing the tokenized portions of a user's full name\n *\n * @param fullName The full name object with the tokenized portions\n * @param formatStyle The style in which the name should be formatted\n * @returns A locale-aware string representation of a person's name\n */\nexport function formatFullName(\n  fullName: AppleAuthenticationFullName,\n  formatStyle?: AppleAuthenticationFullNameFormatStyle\n): string {\n  if (!ExpoAppleAuthentication || !ExpoAppleAuthentication.formatFullName) {\n    throw new UnavailabilityError('expo-apple-authentication', 'formatFullName');\n  }\n  return ExpoAppleAuthentication.formatFullName(fullName, formatStyle);\n}\n\n// @docsMissing\nexport function addRevokeListener(listener: () => void): EventSubscription {\n  return ExpoAppleAuthentication.addListener('Expo.appleIdCredentialRevoked', listener);\n}\n\nexport { EventSubscription as Subscription };\n"]}