-- Merging decision tree log ---
manifest
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:1-44:12
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:1-44:12
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-google-signin_google-signin] K:\2025\thenextdoor\app\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:lottie-react-native] K:\2025\thenextdoor\app\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] K:\2025\thenextdoor\app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] K:\2025\thenextdoor\app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-31:12
MERGED from [:react-native-async-storage_async-storage] K:\2025\thenextdoor\app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo] K:\2025\thenextdoor\app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-get-random-values] K:\2025\thenextdoor\app\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-reanimated] K:\2025\thenextdoor\app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] K:\2025\thenextdoor\app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-dev-menu-interface] K:\2025\thenextdoor\app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be47a8f3586a9a896d25c7213f11c69\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c036dd4e89c039bdb830b38729aba27\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa3c533e38ea4061c6400480659805c3\transformed\expo.modules.systemui-5.0.9\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:2:1-28:12
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d67b401b98683845e20d868c9079182a\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce52cb4064562b4b9386643be0fc7867\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1e7fd93c38007b66fe1ce6dffa1cb17\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b5e14ace161c416b3792cc65073136\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bf1d0ccdaf978e74a899b15f35a1ba1\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2330eff14f5a13aa56b81f00e6eedfcf\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff300a4baf9f1bf29410ae898edb5295\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fcecc4c6bb8b8753bdcf3c3459c4fbc\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3148937ce24975b0c8bd8c282baed048\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd82b284ddc0eb890197a89ff6aa47b0\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4569d0f6272c114cc561a4707d109554\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c448edad99bc61105557ce32ab74a1b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5b6dc9338af08dcda60bafb8897c85b\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85dde86bea35887bac0feb7d53cce7ae\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6d6d9b25fc67e5dd2f7142d2fad5488\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c2022e539491c06e7973071945ca586\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e93b6b7e7c43855a5d878759065fa91\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a4438f452b91905d3d5ba17982c1aa1\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cfee973423e06e528b5d8479c9cb2e9\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:2:1-16:12
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:2:1-57:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b856133ef4db49661284566b09ee7c\transformed\lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a9086f53045ff51dd925c6f1785f9fd\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cafe38d429a8d27ed8b0af5abbc941f2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb3a733336d71e9b6ac296baf101114\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c19e0f1ca92d70c92c7cefc81c5d082\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b6800d76fac0a182af7bfe9b63de68d\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bc4254d4f871328e95ff99fcc97283f\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:2:1-36:12
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\11f5bd776e6bd90607354863cae0358e\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bf03df1e227318d4ab7c4ce04613cbc\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a88919693188ddbdd185f7e98648316\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59b487b2b096ccdefac5e140ae48e3f7\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\979a76771b5c0b857ab3556be228bba8\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] K:\2025\thenextdoor\app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] K:\2025\thenextdoor\app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-loader] K:\2025\thenextdoor\app\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] K:\2025\thenextdoor\app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] K:\2025\thenextdoor\app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] K:\2025\thenextdoor\app\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] K:\2025\thenextdoor\app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0121348f8010607e88e9bdd4de2d5457\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e45f93c4e65d3e73e3912e20442b03d\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.battery:9.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\223fef03a86ae5bfc01377c2e3e6b3e2\transformed\expo.modules.battery-9.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\de3d5618ee54399d98ce771e21eadde8\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.brightness:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1685c9327973d4c80d2a7e7d7ed4d434\transformed\expo.modules.brightness-13.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\97db698a654adb33d1d15e2bf1909bdb\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a5a36a4cfe71a2a1cc359ce3bb38163\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdecb3a2f1c538aa08f216a388624fd7\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e3d6a057b2dd5d12101306558035d08\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:2:1-17:12
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:2:1-14:12
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:2:1-43:12
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:2:1-15:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\7938a318d123ee90383807b2098b71d0\transformed\checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:2:1-72:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\575af74d844f29d868dd5b944ef0f062\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ec3015cc7ff4c481680740b7794101b\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed9844724e6df2c6e41707abe194c32f\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be0cfd1a362effe491512e1846cd003\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb516921c4b8cc8b49625710972a0e75\transformed\media3-session-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae751c6718034b4a877c24e586b4311\transformed\media3-ui-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c48f59b3e2cd3446200dbb7565607e\transformed\media3-extractor-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36edd7059738f337a37360df7a158ed0\transformed\media3-container-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f9000d37c2b2112d39b39670f9b3060\transformed\media3-datasource-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e06c1db6573e33c6f6599be6804f558\transformed\media3-decoder-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f1de9fc05ff90b51e532763533dcca\transformed\media3-database-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f86809e9d01fb51f710892eada243c7\transformed\media3-common-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5b17c2c85fa47e8ec931ea6bd8f4bf3\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1911df4c15dbc9e5b65843339f8d97d7\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2df3c3fff3357cb379a490ac0d5d6d18\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f5e6a87da4744d0cca8c066d541c14c\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb7366dc85ed0409c20c0dc6f226d4\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\592dcb080aec094462c2de0303282218\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e7aeeae9829bb0396e248c6977acea\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\407c6d041b23f1d9f3ea4fa3d2c7a9a1\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb861610dfae40c2ef6fdec5e26b91af\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eeedb2ba318e0dea68f6e148bbfd61a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2fc949d6d75cb7f96da1072a54dd82a\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45d5d6110a28f37b8f973c387cb5322f\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6c5f039a38b430a1949a1708dda5dc\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8770a77af4f549302c0ccc4f3429b863\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\365e09702c024567219d5c8f77f4a389\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\956cf3dbafe9be3d031630c31a62b382\transformed\activity-ktx-1.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c238fa2a5966ecca5284494a7966346\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\829cfe53c64cfc693496f8451f29e7a8\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\081581c012a4283b28daac0fe17aa771\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9db95877b6b5303346dab89be2bb74e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28bc247258e5cff6e6aac54078e1de43\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a177d82bc21b186c301bc9b6c8d7387d\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0744e17698f09f9c09091ef16a6a923\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52e52fc777190b4295f59d0a858517f\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2aaf2f0c5250c0e148ab60ada39c9da\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a907cfc2c979bc8caf6a7799f50a94a8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427513327c43f4b46023352ed3ecf832\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b1db28f9866d87d063c78805394f08\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8772e173494834d4fdfe2745c1bca0b0\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa8d219fb756bbf0447e8d2f088c459\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\183e370a152c2b8981bc4a82353880f3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5529e502845fdf46b49c2089dbb4832\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba621659a277237e85638838dad2dcb6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72ec46a88e05357e6517408e9569c65b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bc00ab5eaca23c3c3ebd49ed8511ff9\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51166ce65ea68e12719e2bca48ab2cfd\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85bd25b59b035eb382f11b5156ead459\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a32c878ec8487f5bc88a7d5189cbcc2\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a585069a2622d4c0408fcef1801fd60\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eb26f338647b3e830a79223a52192d8\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f4f06015bc3afef300a72fce02839a\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fa8e62cff35f0924d9735e7c1c52928\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\618758d8ab77f01a5d167c4fd9df1af4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aecacd41bb3fe9f9b9ff7ee8bbb41880\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\755e416baf87ae9e50f4bc42bec886cd\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15bbe25edd48bc673476cf4bb5ca4a87\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0554aba29ce76817e6a5be45213e21ee\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\714600962765d5961827fa3521ced293\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf6d65a0d97e898f7eca44154f963b3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\850320aa2b4b52ced6f3465503644aaa\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\58726736d2c8b2cb18170698e2d2af0c\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b153c5f07cea60c340fe199e7ef9098d\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\de145cd6b45e8b1fb7e804d6c000ef76\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4851cc4faabf79f6af50acd560b500\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c99fc93f13e5345aee29eaa313ee725\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\86cd36f2af6251908c1cd91569dccf65\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\09f7c3694aac112487e9f730f6c374d4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a40c827f1ab6cae60b0a38160d029dc\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc1cc582935f0fa4fa7ad13b3b8686c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6a1a82e45a7434ecf0c3d775347d44\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cc008949652dbb80e471afb44bf7cf0\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\96cb0a331000a46f2b8e5d4183755d45\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253d34bd1233d4406c569c23b5c30777\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbaea5332f0cb03d7c9e16969244d33b\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7ab8f7120f36931c3468a226fbeffc5\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18cdabc45926f1dc3dff94376b7201d0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d5fcfb1e81a9a7a0ab85df90f606618\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c72240e70788902de7eb47bfe9d21a7b\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa03d1ffcf2d88d54ed6c7d80f87d2da\transformed\hermes-android-0.79.5-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\733dbf0845ebde69fe3f06409df9221b\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac29939bd47a81610899cb008878583\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6109be993cf18bb4f46555146e631798\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bffdab30bd5a7c7e1564164c0aeaa47\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ba03b814d30daf0b666892538f8bffe\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6eb0cec1c9f801500b232161e7a948\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d8c5de9256cbf117d683466032fda1b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e46f645dab24076e9faaca7bbf3eaa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d85edd20cce09bbbf85d26c1546231\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e4976684f95c1b13d07f416f4b774fd\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56974262f1ab57f99a4950187b2797ae\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade14637494d7bb8f411b451549fb2cd\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76a758e3f7e2554c87ebff87124141e\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5424d0a62b576aa1dd6954661a7a105\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\376bc31ec5340584b4d0896635c1e053\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9717af2fa7f7619c5d79a69172a6cee\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af3d4771b467d110290eceabbbcbdf9b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cb2f9a25605a6be165659396486475a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4cb90f99ef5e2ab73584d25744c9ba2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\274a5e4c52765583c47c315e8a011727\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd01ffc08af05631b09907077e7d319\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.jitsi:webrtc:124.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b25fa17bd34aba5bc7c97928d793176\transformed\webrtc-124.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e80cc6deab05b24bdfe1060903f43f89\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\21296707228c89d52d10d784077a3ec8\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:3-78
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:7:5-81
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:7:5-81
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:3-76
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:8:5-79
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:3-76
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:8:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be0cfd1a362effe491512e1846cd003\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be0cfd1a362effe491512e1846cd003\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f86809e9d01fb51f710892eada243c7\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f86809e9d01fb51f710892eada243c7\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fa8e62cff35f0924d9735e7c1c52928\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fa8e62cff35f0924d9735e7c1c52928\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76a758e3f7e2554c87ebff87124141e\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76a758e3f7e2554c87ebff87124141e\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:4:20-74
uses-permission#android.permission.BLUETOOTH
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:3-65
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:5:20-63
uses-permission#android.permission.CAMERA
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:3-62
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:6:20-60
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:3-76
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:7:20-74
uses-permission#android.permission.INTERNET
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:3-64
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:8:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:3-77
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:8:5-80
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:8:5-80
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:9:20-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:3-77
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:12:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:11:5-80
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:11:5-80
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:10:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:3-68
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:7:5-71
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:7:5-71
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:8:5-71
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:8:5-71
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:11:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:3-75
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:3-75
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:3-75
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:12:20-73
uses-permission#android.permission.VIBRATE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a5a36a4cfe71a2a1cc359ce3bb38163\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a5a36a4cfe71a2a1cc359ce3bb38163\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:13:20-61
uses-permission#android.permission.WAKE_LOCK
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:3-65
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:14:20-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:15:3-78
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:11:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:12:5-81
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:12:5-81
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:15:20-76
uses-permission#android.permission.WRITE_SETTINGS
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:16:3-70
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:16:20-68
queries
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:17:3-23:13
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:14:5-25:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:7:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:7:5-13:15
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:10:5-39:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:18:5-22:14
action#android.intent.action.VIEW
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:7-58
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:19:15-56
category#android.intent.category.BROWSABLE
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:7-67
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:20:17-65
data
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:7-37
	android:scheme
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:21:13-35
permission#android.permission.INTERNET
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:3-59
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:24:15-57
application
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:3-43:17
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:3-43:17
MERGED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:3-43:17
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-13:19
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-13:19
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:11:5-26:19
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:11:5-26:19
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:18:5-22:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:27:5-55:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b856133ef4db49661284566b09ee7c\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b856133ef4db49661284566b09ee7c\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a9086f53045ff51dd925c6f1785f9fd\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a9086f53045ff51dd925c6f1785f9fd\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:20:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:10:5-41:19
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:41:5-70:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb7366dc85ed0409c20c0dc6f226d4\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb7366dc85ed0409c20c0dc6f226d4\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\592dcb080aec094462c2de0303282218\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\592dcb080aec094462c2de0303282218\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eeedb2ba318e0dea68f6e148bbfd61a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eeedb2ba318e0dea68f6e148bbfd61a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2fc949d6d75cb7f96da1072a54dd82a\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2fc949d6d75cb7f96da1072a54dd82a\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8770a77af4f549302c0ccc4f3429b863\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8770a77af4f549302c0ccc4f3429b863\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427513327c43f4b46023352ed3ecf832\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427513327c43f4b46023352ed3ecf832\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6a1a82e45a7434ecf0c3d775347d44\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6a1a82e45a7434ecf0c3d775347d44\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cc008949652dbb80e471afb44bf7cf0\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cc008949652dbb80e471afb44bf7cf0\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac29939bd47a81610899cb008878583\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac29939bd47a81610899cb008878583\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e46f645dab24076e9faaca7bbf3eaa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e46f645dab24076e9faaca7bbf3eaa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\21296707228c89d52d10d784077a3ec8\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\21296707228c89d52d10d784077a3ec8\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:248-291
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:248-291
	tools:ignore
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:116-161
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:116-161
	android:icon
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:81-115
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:81-115
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:221-247
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:221-247
	android:label
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:48-80
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:48-80
	android:fullBackupContent
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:292-350
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:292-350
	tools:targetApi
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:allowBackup
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:162-188
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:162-188
	android:theme
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:189-220
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:189-220
	android:dataExtractionRules
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:351-420
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:351-420
	tools:replace
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:16-47
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:25:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:5-83
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:60-81
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:26:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:5-119
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:73-117
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:27:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:5-105
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:81-103
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:28:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:5-99
	android:value
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:80-97
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:29:16-79
activity#com.UNextDoor.app.MainActivity
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:5-42:16
	android:screenOrientation
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:277-313
	android:launchMode
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:135-166
	android:windowSoftInputMode
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:167-206
	android:exported
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:253-276
	android:configChanges
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:44-134
	android:theme
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:207-252
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:30:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:31:7-34:23
action#android.intent.action.MAIN
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:9-60
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:32:17-58
category#android.intent.category.LAUNCHER
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:33:9-68
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:33:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:UNextDoor+data:scheme:exp+unextdoor
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:35:7-41:23
category#android.intent.category.DEFAULT
ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:9-67
	android:name
		ADDED from K:\2025\thenextdoor\app\android\app\src\main\AndroidManifest.xml:37:19-65
uses-sdk
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-google-signin_google-signin] K:\2025\thenextdoor\app\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] K:\2025\thenextdoor\app\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] K:\2025\thenextdoor\app\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] K:\2025\thenextdoor\app\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] K:\2025\thenextdoor\app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] K:\2025\thenextdoor\app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] K:\2025\thenextdoor\app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] K:\2025\thenextdoor\app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] K:\2025\thenextdoor\app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] K:\2025\thenextdoor\app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] K:\2025\thenextdoor\app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] K:\2025\thenextdoor\app\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] K:\2025\thenextdoor\app\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] K:\2025\thenextdoor\app\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] K:\2025\thenextdoor\app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] K:\2025\thenextdoor\app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] K:\2025\thenextdoor\app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] K:\2025\thenextdoor\app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] K:\2025\thenextdoor\app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] K:\2025\thenextdoor\app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be47a8f3586a9a896d25c7213f11c69\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be47a8f3586a9a896d25c7213f11c69\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c036dd4e89c039bdb830b38729aba27\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c036dd4e89c039bdb830b38729aba27\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa3c533e38ea4061c6400480659805c3\transformed\expo.modules.systemui-5.0.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa3c533e38ea4061c6400480659805c3\transformed\expo.modules.systemui-5.0.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d67b401b98683845e20d868c9079182a\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d67b401b98683845e20d868c9079182a\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce52cb4064562b4b9386643be0fc7867\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce52cb4064562b4b9386643be0fc7867\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1e7fd93c38007b66fe1ce6dffa1cb17\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1e7fd93c38007b66fe1ce6dffa1cb17\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b5e14ace161c416b3792cc65073136\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\04b5e14ace161c416b3792cc65073136\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bf1d0ccdaf978e74a899b15f35a1ba1\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bf1d0ccdaf978e74a899b15f35a1ba1\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2330eff14f5a13aa56b81f00e6eedfcf\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2330eff14f5a13aa56b81f00e6eedfcf\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff300a4baf9f1bf29410ae898edb5295\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff300a4baf9f1bf29410ae898edb5295\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fcecc4c6bb8b8753bdcf3c3459c4fbc\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fcecc4c6bb8b8753bdcf3c3459c4fbc\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3148937ce24975b0c8bd8c282baed048\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3148937ce24975b0c8bd8c282baed048\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd82b284ddc0eb890197a89ff6aa47b0\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd82b284ddc0eb890197a89ff6aa47b0\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4569d0f6272c114cc561a4707d109554\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4569d0f6272c114cc561a4707d109554\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c448edad99bc61105557ce32ab74a1b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c448edad99bc61105557ce32ab74a1b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5b6dc9338af08dcda60bafb8897c85b\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5b6dc9338af08dcda60bafb8897c85b\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85dde86bea35887bac0feb7d53cce7ae\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85dde86bea35887bac0feb7d53cce7ae\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6d6d9b25fc67e5dd2f7142d2fad5488\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6d6d9b25fc67e5dd2f7142d2fad5488\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c2022e539491c06e7973071945ca586\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c2022e539491c06e7973071945ca586\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e93b6b7e7c43855a5d878759065fa91\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e93b6b7e7c43855a5d878759065fa91\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a4438f452b91905d3d5ba17982c1aa1\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a4438f452b91905d3d5ba17982c1aa1\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cfee973423e06e528b5d8479c9cb2e9\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cfee973423e06e528b5d8479c9cb2e9\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.audio:expo.modules.audio:0.4.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\502441929375cd55b0faa2dddcef75d8\transformed\expo.modules.audio-0.4.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b856133ef4db49661284566b09ee7c\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b856133ef4db49661284566b09ee7c\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a9086f53045ff51dd925c6f1785f9fd\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a9086f53045ff51dd925c6f1785f9fd\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cafe38d429a8d27ed8b0af5abbc941f2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cafe38d429a8d27ed8b0af5abbc941f2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb3a733336d71e9b6ac296baf101114\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb3a733336d71e9b6ac296baf101114\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c19e0f1ca92d70c92c7cefc81c5d082\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c19e0f1ca92d70c92c7cefc81c5d082\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b6800d76fac0a182af7bfe9b63de68d\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b6800d76fac0a182af7bfe9b63de68d\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bc4254d4f871328e95ff99fcc97283f\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bc4254d4f871328e95ff99fcc97283f\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\11f5bd776e6bd90607354863cae0358e\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\11f5bd776e6bd90607354863cae0358e\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bf03df1e227318d4ab7c4ce04613cbc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bf03df1e227318d4ab7c4ce04613cbc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a88919693188ddbdd185f7e98648316\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a88919693188ddbdd185f7e98648316\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59b487b2b096ccdefac5e140ae48e3f7\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\59b487b2b096ccdefac5e140ae48e3f7\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\979a76771b5c0b857ab3556be228bba8\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\979a76771b5c0b857ab3556be228bba8\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] K:\2025\thenextdoor\app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] K:\2025\thenextdoor\app\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] K:\2025\thenextdoor\app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] K:\2025\thenextdoor\app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] K:\2025\thenextdoor\app\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] K:\2025\thenextdoor\app\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] K:\2025\thenextdoor\app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] K:\2025\thenextdoor\app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] K:\2025\thenextdoor\app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] K:\2025\thenextdoor\app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] K:\2025\thenextdoor\app\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] K:\2025\thenextdoor\app\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] K:\2025\thenextdoor\app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] K:\2025\thenextdoor\app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0121348f8010607e88e9bdd4de2d5457\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0121348f8010607e88e9bdd4de2d5457\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e45f93c4e65d3e73e3912e20442b03d\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e45f93c4e65d3e73e3912e20442b03d\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.battery:9.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\223fef03a86ae5bfc01377c2e3e6b3e2\transformed\expo.modules.battery-9.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.battery:9.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\223fef03a86ae5bfc01377c2e3e6b3e2\transformed\expo.modules.battery-9.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\de3d5618ee54399d98ce771e21eadde8\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\de3d5618ee54399d98ce771e21eadde8\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.brightness:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1685c9327973d4c80d2a7e7d7ed4d434\transformed\expo.modules.brightness-13.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.brightness:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1685c9327973d4c80d2a7e7d7ed4d434\transformed\expo.modules.brightness-13.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\97db698a654adb33d1d15e2bf1909bdb\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\97db698a654adb33d1d15e2bf1909bdb\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a5a36a4cfe71a2a1cc359ce3bb38163\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a5a36a4cfe71a2a1cc359ce3bb38163\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdecb3a2f1c538aa08f216a388624fd7\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdecb3a2f1c538aa08f216a388624fd7\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e3d6a057b2dd5d12101306558035d08\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e3d6a057b2dd5d12101306558035d08\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\7938a318d123ee90383807b2098b71d0\transformed\checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.13\transforms\7938a318d123ee90383807b2098b71d0\transformed\checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:6:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\575af74d844f29d868dd5b944ef0f062\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\575af74d844f29d868dd5b944ef0f062\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ec3015cc7ff4c481680740b7794101b\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ec3015cc7ff4c481680740b7794101b\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed9844724e6df2c6e41707abe194c32f\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed9844724e6df2c6e41707abe194c32f\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be0cfd1a362effe491512e1846cd003\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5be0cfd1a362effe491512e1846cd003\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb516921c4b8cc8b49625710972a0e75\transformed\media3-session-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb516921c4b8cc8b49625710972a0e75\transformed\media3-session-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae751c6718034b4a877c24e586b4311\transformed\media3-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae751c6718034b4a877c24e586b4311\transformed\media3-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c48f59b3e2cd3446200dbb7565607e\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c48f59b3e2cd3446200dbb7565607e\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36edd7059738f337a37360df7a158ed0\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36edd7059738f337a37360df7a158ed0\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f9000d37c2b2112d39b39670f9b3060\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f9000d37c2b2112d39b39670f9b3060\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e06c1db6573e33c6f6599be6804f558\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e06c1db6573e33c6f6599be6804f558\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f1de9fc05ff90b51e532763533dcca\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f1de9fc05ff90b51e532763533dcca\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f86809e9d01fb51f710892eada243c7\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f86809e9d01fb51f710892eada243c7\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5b17c2c85fa47e8ec931ea6bd8f4bf3\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5b17c2c85fa47e8ec931ea6bd8f4bf3\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1911df4c15dbc9e5b65843339f8d97d7\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1911df4c15dbc9e5b65843339f8d97d7\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2df3c3fff3357cb379a490ac0d5d6d18\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2df3c3fff3357cb379a490ac0d5d6d18\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f5e6a87da4744d0cca8c066d541c14c\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f5e6a87da4744d0cca8c066d541c14c\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb7366dc85ed0409c20c0dc6f226d4\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb7366dc85ed0409c20c0dc6f226d4\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\592dcb080aec094462c2de0303282218\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\592dcb080aec094462c2de0303282218\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e7aeeae9829bb0396e248c6977acea\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3e7aeeae9829bb0396e248c6977acea\transformed\play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\407c6d041b23f1d9f3ea4fa3d2c7a9a1\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\407c6d041b23f1d9f3ea4fa3d2c7a9a1\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb861610dfae40c2ef6fdec5e26b91af\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb861610dfae40c2ef6fdec5e26b91af\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eeedb2ba318e0dea68f6e148bbfd61a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4eeedb2ba318e0dea68f6e148bbfd61a\transformed\play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2fc949d6d75cb7f96da1072a54dd82a\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2fc949d6d75cb7f96da1072a54dd82a\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45d5d6110a28f37b8f973c387cb5322f\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45d5d6110a28f37b8f973c387cb5322f\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6c5f039a38b430a1949a1708dda5dc\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6c5f039a38b430a1949a1708dda5dc\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8770a77af4f549302c0ccc4f3429b863\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8770a77af4f549302c0ccc4f3429b863\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\365e09702c024567219d5c8f77f4a389\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\365e09702c024567219d5c8f77f4a389\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\956cf3dbafe9be3d031630c31a62b382\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\956cf3dbafe9be3d031630c31a62b382\transformed\activity-ktx-1.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c238fa2a5966ecca5284494a7966346\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c238fa2a5966ecca5284494a7966346\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\829cfe53c64cfc693496f8451f29e7a8\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\829cfe53c64cfc693496f8451f29e7a8\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\081581c012a4283b28daac0fe17aa771\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\081581c012a4283b28daac0fe17aa771\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9db95877b6b5303346dab89be2bb74e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9db95877b6b5303346dab89be2bb74e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28bc247258e5cff6e6aac54078e1de43\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28bc247258e5cff6e6aac54078e1de43\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a177d82bc21b186c301bc9b6c8d7387d\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a177d82bc21b186c301bc9b6c8d7387d\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0744e17698f09f9c09091ef16a6a923\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0744e17698f09f9c09091ef16a6a923\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52e52fc777190b4295f59d0a858517f\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52e52fc777190b4295f59d0a858517f\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2aaf2f0c5250c0e148ab60ada39c9da\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2aaf2f0c5250c0e148ab60ada39c9da\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a907cfc2c979bc8caf6a7799f50a94a8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a907cfc2c979bc8caf6a7799f50a94a8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427513327c43f4b46023352ed3ecf832\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\427513327c43f4b46023352ed3ecf832\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b1db28f9866d87d063c78805394f08\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b1db28f9866d87d063c78805394f08\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8772e173494834d4fdfe2745c1bca0b0\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8772e173494834d4fdfe2745c1bca0b0\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa8d219fb756bbf0447e8d2f088c459\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa8d219fb756bbf0447e8d2f088c459\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\183e370a152c2b8981bc4a82353880f3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\183e370a152c2b8981bc4a82353880f3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5529e502845fdf46b49c2089dbb4832\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5529e502845fdf46b49c2089dbb4832\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba621659a277237e85638838dad2dcb6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba621659a277237e85638838dad2dcb6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72ec46a88e05357e6517408e9569c65b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72ec46a88e05357e6517408e9569c65b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bc00ab5eaca23c3c3ebd49ed8511ff9\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bc00ab5eaca23c3c3ebd49ed8511ff9\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51166ce65ea68e12719e2bca48ab2cfd\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51166ce65ea68e12719e2bca48ab2cfd\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85bd25b59b035eb382f11b5156ead459\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85bd25b59b035eb382f11b5156ead459\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a32c878ec8487f5bc88a7d5189cbcc2\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a32c878ec8487f5bc88a7d5189cbcc2\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a585069a2622d4c0408fcef1801fd60\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a585069a2622d4c0408fcef1801fd60\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eb26f338647b3e830a79223a52192d8\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0eb26f338647b3e830a79223a52192d8\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f4f06015bc3afef300a72fce02839a\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4f4f06015bc3afef300a72fce02839a\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fa8e62cff35f0924d9735e7c1c52928\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fa8e62cff35f0924d9735e7c1c52928\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\618758d8ab77f01a5d167c4fd9df1af4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\618758d8ab77f01a5d167c4fd9df1af4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aecacd41bb3fe9f9b9ff7ee8bbb41880\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aecacd41bb3fe9f9b9ff7ee8bbb41880\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\755e416baf87ae9e50f4bc42bec886cd\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\755e416baf87ae9e50f4bc42bec886cd\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15bbe25edd48bc673476cf4bb5ca4a87\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15bbe25edd48bc673476cf4bb5ca4a87\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0554aba29ce76817e6a5be45213e21ee\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0554aba29ce76817e6a5be45213e21ee\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\714600962765d5961827fa3521ced293\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\714600962765d5961827fa3521ced293\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf6d65a0d97e898f7eca44154f963b3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf6d65a0d97e898f7eca44154f963b3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\850320aa2b4b52ced6f3465503644aaa\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\850320aa2b4b52ced6f3465503644aaa\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\58726736d2c8b2cb18170698e2d2af0c\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\58726736d2c8b2cb18170698e2d2af0c\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b153c5f07cea60c340fe199e7ef9098d\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b153c5f07cea60c340fe199e7ef9098d\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\de145cd6b45e8b1fb7e804d6c000ef76\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\de145cd6b45e8b1fb7e804d6c000ef76\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4851cc4faabf79f6af50acd560b500\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4851cc4faabf79f6af50acd560b500\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c99fc93f13e5345aee29eaa313ee725\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c99fc93f13e5345aee29eaa313ee725\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\86cd36f2af6251908c1cd91569dccf65\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\86cd36f2af6251908c1cd91569dccf65\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\09f7c3694aac112487e9f730f6c374d4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\09f7c3694aac112487e9f730f6c374d4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a40c827f1ab6cae60b0a38160d029dc\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a40c827f1ab6cae60b0a38160d029dc\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc1cc582935f0fa4fa7ad13b3b8686c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc1cc582935f0fa4fa7ad13b3b8686c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6a1a82e45a7434ecf0c3d775347d44\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6a1a82e45a7434ecf0c3d775347d44\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cc008949652dbb80e471afb44bf7cf0\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cc008949652dbb80e471afb44bf7cf0\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\96cb0a331000a46f2b8e5d4183755d45\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\96cb0a331000a46f2b8e5d4183755d45\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253d34bd1233d4406c569c23b5c30777\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253d34bd1233d4406c569c23b5c30777\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbaea5332f0cb03d7c9e16969244d33b\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fbaea5332f0cb03d7c9e16969244d33b\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7ab8f7120f36931c3468a226fbeffc5\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7ab8f7120f36931c3468a226fbeffc5\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18cdabc45926f1dc3dff94376b7201d0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18cdabc45926f1dc3dff94376b7201d0\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d5fcfb1e81a9a7a0ab85df90f606618\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d5fcfb1e81a9a7a0ab85df90f606618\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c72240e70788902de7eb47bfe9d21a7b\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c72240e70788902de7eb47bfe9d21a7b\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa03d1ffcf2d88d54ed6c7d80f87d2da\transformed\hermes-android-0.79.5-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa03d1ffcf2d88d54ed6c7d80f87d2da\transformed\hermes-android-0.79.5-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\733dbf0845ebde69fe3f06409df9221b\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\733dbf0845ebde69fe3f06409df9221b\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac29939bd47a81610899cb008878583\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac29939bd47a81610899cb008878583\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6109be993cf18bb4f46555146e631798\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6109be993cf18bb4f46555146e631798\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bffdab30bd5a7c7e1564164c0aeaa47\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bffdab30bd5a7c7e1564164c0aeaa47\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ba03b814d30daf0b666892538f8bffe\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ba03b814d30daf0b666892538f8bffe\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6eb0cec1c9f801500b232161e7a948\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6eb0cec1c9f801500b232161e7a948\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d8c5de9256cbf117d683466032fda1b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d8c5de9256cbf117d683466032fda1b\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e46f645dab24076e9faaca7bbf3eaa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e46f645dab24076e9faaca7bbf3eaa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d85edd20cce09bbbf85d26c1546231\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d85edd20cce09bbbf85d26c1546231\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e4976684f95c1b13d07f416f4b774fd\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e4976684f95c1b13d07f416f4b774fd\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56974262f1ab57f99a4950187b2797ae\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56974262f1ab57f99a4950187b2797ae\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade14637494d7bb8f411b451549fb2cd\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ade14637494d7bb8f411b451549fb2cd\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76a758e3f7e2554c87ebff87124141e\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76a758e3f7e2554c87ebff87124141e\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5424d0a62b576aa1dd6954661a7a105\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5424d0a62b576aa1dd6954661a7a105\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\376bc31ec5340584b4d0896635c1e053\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\376bc31ec5340584b4d0896635c1e053\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9717af2fa7f7619c5d79a69172a6cee\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9717af2fa7f7619c5d79a69172a6cee\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af3d4771b467d110290eceabbbcbdf9b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af3d4771b467d110290eceabbbcbdf9b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cb2f9a25605a6be165659396486475a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cb2f9a25605a6be165659396486475a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4cb90f99ef5e2ab73584d25744c9ba2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4cb90f99ef5e2ab73584d25744c9ba2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\274a5e4c52765583c47c315e8a011727\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\274a5e4c52765583c47c315e8a011727\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd01ffc08af05631b09907077e7d319\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd01ffc08af05631b09907077e7d319\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [org.jitsi:webrtc:124.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b25fa17bd34aba5bc7c97928d793176\transformed\webrtc-124.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [org.jitsi:webrtc:124.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b25fa17bd34aba5bc7c97928d793176\transformed\webrtc-124.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e80cc6deab05b24bdfe1060903f43f89\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e80cc6deab05b24bdfe1060903f43f89\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\21296707228c89d52d10d784077a3ec8\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\21296707228c89d52d10d784077a3ec8\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb3a733336d71e9b6ac296baf101114\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from K:\2025\thenextdoor\app\android\app\src\debug\AndroidManifest.xml
intent#action:name:org.chromium.intent.action.PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#org.chromium.intent.action.PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
intent#action:name:org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
action#org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
intent#action:name:org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
action#org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
	android:authorities
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
	android:exported
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
	android:resource
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
	android:name
		ADDED from [:react-native-webview] K:\2025\thenextdoor\app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\227999a1dae6ef451ce49fda2e1f6794\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [:react-native-community_netinfo] K:\2025\thenextdoor\app\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
activity#com.razorpay.CheckoutActivity
ADDED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [:react-native-razorpay] K:\2025\thenextdoor\app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
service#com.oney.WebRTCModule.MediaProjectionService
ADDED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-12:19
	android:foregroundServiceType
		ADDED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-60
	android:name
		ADDED from [:react-native-webrtc] K:\2025\thenextdoor\app\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
package#host.exp.exponent
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] K:\2025\thenextdoor\app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] K:\2025\thenextdoor\app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a99a6f26d36648368ec505176d6c6ea3\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] K:\2025\thenextdoor\app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:8:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:5-92
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:9:22-89
activity#expo.modules.video.FullscreenPlayerActivity
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:12:9-16:49
	android:supportsPictureInPicture
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:15:13-52
	android:configChanges
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:14:13-91
	android:theme
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:16:13-46
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:13:13-71
service#expo.modules.video.playbackService.ExpoVideoPlaybackService
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:18:9-25:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:20:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:21:13-58
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:19:13-87
intent-filter#action:name:androidx.media3.session.MediaSessionService
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:22:13-24:29
action#androidx.media3.session.MediaSessionService
ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:17-86
	android:name
		ADDED from [host.exp.exponent:expo.modules.video:2.2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fb139aeb1fb8f19f20e64252e619c09\transformed\expo.modules.video-2.2.2\AndroidManifest.xml:23:25-83
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:11:9-13:42
	android:value
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:13:13-39
	android:name
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\7965e8318524ff48e3c0d33698c451d9\transformed\expo.modules.camera-16.1.9\AndroidManifest.xml:12:13-64
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
MERGED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:31:9-33:39
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
	android:theme
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\716ca713417e72d78602ee0e6f8c2057\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5638a5b46ced7a28b67584a4e9b2a9d8\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8dac97d5162c012dc4e0034eed0655\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
action#android.intent.action.GET_CONTENT
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
category#android.intent.category.OPENABLE
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
	android:grantUriPermissions
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
	android:exported
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
service#expo.modules.location.services.LocationTaskService
ADDED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:12:13-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:7:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:5-75
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:8:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:5-75
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:9:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:5-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.medialibrary:17.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\27c813fb35621ab83e69b91a71f06aa0\transformed\expo.modules.medialibrary-17.1.7\AndroidManifest.xml:10:22-87
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1240b63f01be300aab5541191276cc90\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
intent#action:name:android.intent.action.TTS_SERVICE
ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:8:9-12:18
action#android.intent.action.TTS_SERVICE
ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91b357e811a4b8fbb4dedca6fe6f7ba\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:21-69
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:28:9-30:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:37:21-58
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.13\transforms\c09f95c3020b160439be05f0041910f6\transformed\standard-core-1.6.53\AndroidManifest.xml:68:13-61
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3afca45618f18dc581937d02b2bdc40f\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8afee9e7e07808ffb0e5ff41410d2807\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\253e5188189eb42fb42519a40648537a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b0e9068d083797e5391f7d235389ef3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee76a532c1f96d5df8cddfcd5b46e813\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1968c2bbfd8c06505be4b62b5869f479\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0f256b58234a71591d4450425656475\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c189ac8905302687b747862d3c8a3e\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65982bb9cba12f8a43f529098dc0c561\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b51203f001dd8a68e302f739d7aeb6aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.UNextDoor.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26638c1f5a9cd79c0db4383d52470a9\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d4dbd989ed5f6d378bdd1da52311067\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35f65d1bb0d9212046059db55d50ed5\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7141883094dddd98d25731170e05b482\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
