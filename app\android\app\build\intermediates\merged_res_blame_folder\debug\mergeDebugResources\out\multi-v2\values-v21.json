{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,248,344,441,509,588,676,764,852,940,1027,1114,1201,1288,1384,1474,1570,1660,1753,1860,1965,2084,2209,2330,2543,2802,3073,3291,3523,3759,4009,4222,4431,4662,4863,4979,5149,5470,6499,6956,7460,7968,8477,8991,9496,10000,10505,11011,11513,12019,12528,13036,13535,14042,14550,14842,15136,15436,15736,16065,16406,16544,16688,16844,17237,17455,17677,17903,18119,18229,18399,18589,18830,19089", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,94,95,96,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,243,339,436,504,583,671,759,847,935,1022,1109,1196,1283,1379,1469,1565,1655,1748,1855,1960,2079,2204,2325,2538,2797,3068,3286,3518,3754,4004,4217,4426,4657,4858,4974,5144,5465,6494,6951,7455,7963,8472,8986,9491,9995,10500,11006,11508,12014,12523,13031,13530,14037,14545,14837,15131,15431,15731,16060,16401,16539,16683,16839,17232,17450,17672,17898,18114,18224,18394,18584,18825,19084,19261"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,21,22,23,24,85,86,87,88,90,91,92,95,98,193,196,199,202,208,211,214,281,284,285,288,298,309,369,378,387,396,405,414,423,432,441,450,459,468,477,486,495,504,510,516,522,528,532,536,537,538,539,543,546,549,552,563,564,567,570,574,578", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "232,330,425,521,618,686,765,853,941,1029,1117,1204,1291,1378,1660,1756,1846,1942,7399,7492,7599,7704,7926,8051,8172,8385,8644,14825,15043,15275,15511,15960,16173,16382,21169,21370,21486,21656,22264,23293,27040,27544,28052,28561,29075,29580,30084,30589,31095,31597,32103,32612,33120,33619,34126,34634,34926,35220,35520,35820,36149,36490,36628,36772,36928,37321,37539,37761,37987,38727,38837,39007,39197,39438,39697", "endLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,21,22,23,24,85,86,87,88,90,91,94,97,100,195,198,201,204,210,213,216,283,284,287,292,308,314,377,386,395,404,413,422,431,440,449,458,467,476,485,494,503,509,515,521,527,531,535,536,537,538,542,545,548,551,554,563,566,569,573,577,580", "endColumns": "97,94,95,96,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "325,420,516,613,681,760,848,936,1024,1112,1199,1286,1373,1460,1751,1841,1937,2027,7487,7594,7699,7818,8046,8167,8380,8639,8910,15038,15270,15506,15756,16168,16377,16608,21365,21481,21651,21972,23288,23745,27539,28047,28556,29070,29575,30079,30584,31090,31592,32098,32607,33115,33614,34121,34629,34921,35215,35515,35815,36144,36485,36623,36767,36923,37316,37534,37756,37982,38198,38832,39002,39192,39433,39692,39869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,18,19,20,353,354,361,365,555,558", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1465,1529,1596,25902,26018,26463,26751,38203,38375", "endLines": "2,18,19,20,353,354,361,365,557,562", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1524,1591,1655,26013,26139,26584,26874,38370,38722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,42,43,44,45,47,49,50,51,52,53,55,57,59,61,63,65,66,71,73,75,76,77,79,81,82,83,84,89,101,144,147,190,205,217,219,221,223,226,230,233,234,235,238,239,240,241,242,243,246,247,249,251,253,255,259,261,262,263,264,266,270,272,274,275,276,277,278,279,315,316,317,327,328,329,341", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2032,2123,2226,2329,2434,2541,2650,2759,2868,2977,3086,3193,3296,3415,3570,3725,3830,3951,4052,4199,4340,4443,4562,4669,4772,4927,5098,5247,5412,5569,5720,5839,6190,6339,6488,6600,6747,6900,7047,7122,7211,7298,7823,8915,11673,11858,14628,15761,16613,16736,16859,16972,17155,17410,17611,17700,17811,18044,18145,18240,18363,18492,18609,18786,18885,19020,19163,19298,19417,19618,19737,19830,19941,19997,20104,20299,20410,20543,20638,20729,20820,20913,21030,23750,23821,23904,24527,24584,24642,25266", "endLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,39,41,42,43,44,46,48,49,50,51,52,54,56,58,60,62,64,65,70,72,74,75,76,78,80,81,82,83,84,89,143,146,189,192,207,218,220,222,225,229,232,233,234,237,238,239,240,241,242,245,246,248,250,252,254,258,260,261,262,263,265,269,271,273,274,275,276,277,278,280,315,316,326,327,328,340,352", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2118,2221,2324,2429,2536,2645,2754,2863,2972,3081,3188,3291,3410,3565,3720,3825,3946,4047,4194,4335,4438,4557,4664,4767,4922,5093,5242,5407,5564,5715,5834,6185,6334,6483,6595,6742,6895,7042,7117,7206,7293,7394,7921,11668,11853,14623,14820,15955,16731,16854,16967,17150,17405,17606,17695,17806,18039,18140,18235,18358,18487,18604,18781,18880,19015,19158,19293,19412,19613,19732,19825,19936,19992,20099,20294,20405,20538,20633,20724,20815,20908,21025,21164,23816,23899,24522,24579,24637,25261,25897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,293", "startColumns": "4,4", "startOffsets": "173,21977", "endLines": "3,297", "endColumns": "58,10", "endOffsets": "227,22259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,217,374,536", "endLines": "4,7,10,13", "endColumns": "10,10,10,10", "endOffsets": "212,369,531,692"}, "to": {"startLines": "355,358,362,366", "startColumns": "4,4,4,4", "startOffsets": "26144,26306,26589,26879", "endLines": "357,360,364,368", "endColumns": "10,10,10,10", "endOffsets": "26301,26458,26746,27035"}}]}]}