{"logs": [{"outputFile": "com.UNextDoor.app-mergeDebugResources-79:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3e7aeeae9829bb0396e248c6977acea\\transformed\\play-services-wallet-18.1.3\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "279", "startColumns": "4", "startOffsets": "22246", "endColumns": "75", "endOffsets": "22317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "89,165,166,167", "startColumns": "4,4,4,4", "startOffsets": "7446,13102,13202,13308", "endColumns": "90,99,105,101", "endOffsets": "7532,13197,13303,13405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6234", "endColumns": "117", "endOffsets": "6347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5269,5372,5526,5651,5755,5894,6019,6131,6352,6488,6592,6737,6860,6994,7139,7199,7259", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "5367,5521,5646,5750,5889,6014,6126,6229,6483,6587,6732,6855,6989,7134,7194,7254,7335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1455,1518,1578,1637,1700,1761,1815,1917,1974,2033,2087,2155,2266,2347,2422,2509,2589,2671,2803,2874,2947,3071,3159,3235,3288,3342,3408,3481,3557,3628,3706,3776,3851,3933,4001,4102,4187,4257,4347,4438,4512,4585,4674,4725,4806,4873,4955,5040,5102,5166,5229,5297,5391,5486,5576,5673,5730,5788,5863,5945,6020", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1450,1513,1573,1632,1695,1756,1810,1912,1969,2028,2082,2150,2261,2342,2417,2504,2584,2666,2798,2869,2942,3066,3154,3230,3283,3337,3403,3476,3552,3623,3701,3771,3846,3928,3996,4097,4182,4252,4342,4433,4507,4580,4669,4720,4801,4868,4950,5035,5097,5161,5224,5292,5386,5481,5571,5668,5725,5783,5858,5940,6015,6091"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,93,94,164,184,187,189,190,191,192,193,194,195,196,197,198,199,200,201,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,263,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,3788,3865,3940,4017,4117,4905,4998,5111,7763,7823,13014,15002,15222,15353,15445,15508,15568,15627,15690,15751,15805,15907,15964,16023,16077,16145,16950,17031,17106,17193,17273,17355,17487,17558,17631,17755,17843,17919,17972,18026,18092,18165,18241,18312,18390,18460,18535,18617,18685,18786,18871,18941,19031,19122,19196,19269,19358,19409,19490,19557,19639,19724,19786,19850,19913,19981,20075,20170,20260,20357,20414,20472,21001,21083,21158", "endLines": "25,54,55,56,57,58,66,67,68,93,94,164,184,187,189,190,191,192,193,194,195,196,197,198,199,200,201,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,263,264,265", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "1058,3860,3935,4012,4112,4203,4993,5106,5186,7818,7883,13097,15067,15280,15440,15503,15563,15622,15685,15746,15800,15902,15959,16018,16072,16140,16251,17026,17101,17188,17268,17350,17482,17553,17626,17750,17838,17914,17967,18021,18087,18160,18236,18307,18385,18455,18530,18612,18680,18781,18866,18936,19026,19117,19191,19264,19353,19404,19485,19552,19634,19719,19781,19845,19908,19976,20070,20165,20255,20352,20409,20467,20542,21078,21153,21229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1063,1168,1268,1376,1460,1562,1678,1757,1835,1926,2020,2114,2208,2308,2401,2496,2589,2680,2772,2853,2958,3061,3159,3264,3366,3468,3622,20919", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "1163,1263,1371,1455,1557,1673,1752,1830,1921,2015,2109,2203,2303,2396,2491,2584,2675,2767,2848,2953,3056,3154,3259,3361,3463,3617,3714,20996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "59,60,61,62,63,64,65,274", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4208,4302,4404,4501,4598,4699,4799,21851", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "4297,4399,4496,4593,4694,4794,4900,21947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae751c6718034b4a877c24e586b4311\\transformed\\media3-ui-1.4.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,523,757,836,914,990,1075,1159,1221,1283,1372,1458,1523,1587,1650,1718,1838,1948,2066,2137,2214,2283,2344,2434,2523,2587,2650,2704,2775,2823,2884,2943,3010,3071,3134,3195,3252,3318,3370,3424,3492,3560,3614", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "279,518,752,831,909,985,1070,1154,1216,1278,1367,1453,1518,1582,1645,1713,1833,1943,2061,2132,2209,2278,2339,2429,2518,2582,2645,2699,2770,2818,2879,2938,3005,3066,3129,3190,3247,3313,3365,3419,3487,3555,3609,3675"}, "to": {"startLines": "2,11,16,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,145,146,147,148,149,150,151,152,153,154,155,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,618,9347,9426,9504,9580,9665,9749,9811,9873,9962,10048,10113,10177,10240,10308,10428,10538,10656,10727,10804,10873,10934,11024,11113,11177,11854,11908,11979,12027,12088,12147,12214,12275,12338,12399,12456,12652,12704,12758,12826,12894,12948", "endLines": "10,15,20,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,145,146,147,148,149,150,151,152,153,154,155,158,159,160,161,162,163", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "374,613,847,9421,9499,9575,9660,9744,9806,9868,9957,10043,10108,10172,10235,10303,10423,10533,10651,10722,10799,10868,10929,11019,11108,11172,11235,11903,11974,12022,12083,12142,12209,12270,12333,12394,12451,12517,12699,12753,12821,12889,12943,13009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\35a00de50ceac9bdbb8fc43812824536\\transformed\\android-image-cropper-4.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,109,153,217,278,345,397", "endColumns": "53,43,63,60,66,51,61", "endOffsets": "104,148,212,273,340,392,454"}, "to": {"startLines": "90,179,180,181,182,183,257", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7537,14714,14758,14822,14883,14950,20547", "endColumns": "53,43,63,60,66,51,61", "endOffsets": "7586,14753,14817,14878,14945,14997,20604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb516921c4b8cc8b49625710972a0e75\\transformed\\media3-session-1.4.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,246,315,412,497,574,665,751,850,938,1007,1095,1192,1277,1349,1437,1506,1595,1663,1730,1808,1889,1974", "endColumns": "80,109,68,96,84,76,90,85,98,87,68,87,96,84,71,87,68,88,67,66,77,80,84,90", "endOffsets": "131,241,310,407,492,569,660,746,845,933,1002,1090,1187,1272,1344,1432,1501,1590,1658,1725,1803,1884,1969,2060"}, "to": {"startLines": "92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7682,7888,7998,8067,8164,8249,8326,8417,8503,8602,8690,8759,8847,8944,9029,9101,9189,9258,16256,16324,16391,16469,16550,16635", "endColumns": "80,109,68,96,84,76,90,85,98,87,68,87,96,84,71,87,68,88,67,66,77,80,84,90", "endOffsets": "7758,7993,8062,8159,8244,8321,8412,8498,8597,8685,8754,8842,8939,9024,9096,9184,9253,9342,16319,16386,16464,16545,16630,16721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,252,370,499,622,753,867,994,1088,1231,1373", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "156,247,365,494,617,748,862,989,1083,1226,1368,1481"}, "to": {"startLines": "88,91,168,169,170,171,172,173,174,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7340,7591,13410,13528,13657,13780,13911,14025,14152,14246,14389,14531", "endColumns": "105,90,117,128,122,130,113,126,93,142,141,112", "endOffsets": "7441,7677,13523,13652,13775,13906,14020,14147,14241,14384,14526,14639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3436,3500", "endColumns": "63,65", "endOffsets": "3495,3561"}, "to": {"startLines": "156,157", "startColumns": "4,4", "startOffsets": "12522,12586", "endColumns": "63,65", "endOffsets": "12581,12647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "53,69,178,185,186,188,208,209,210,258,259,260,261,266,267,268,269,270,271,272,273,275,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3719,5191,14644,15072,15141,15285,16726,16794,16872,20609,20691,20770,20841,21234,21314,21387,21467,21545,21620,21692,21764,21952,22023,22102,22171", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3783,5264,14709,15136,15217,15348,16789,16867,16945,20686,20765,20836,20914,21309,21382,21462,21540,21615,21687,21759,21846,22018,22097,22166,22241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5be0cfd1a362effe491512e1846cd003\\transformed\\media3-exoplayer-1.4.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11240,11305,11364,11431,11496,11570,11632,11712,11792", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "11300,11359,11426,11491,11565,11627,11707,11787,11849"}}]}]}